{"permissions": {"allow": ["<PERSON><PERSON>(timeout 30 python3 music.py)", "<PERSON><PERSON>(sudo apt-get:*)", "Bash(sudo apt-get install:*)", "<PERSON><PERSON>(sox:*)", "Bash(grep:*)", "<PERSON><PERSON>(python3:*)", "Bash(pip install:*)", "Bash(rm:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(ls:*)", "Bash(pgrep:*)", "Bash(ps:*)", "Bash(rg:*)", "Bash(find:*)", "<PERSON><PERSON>(true)", "<PERSON><PERSON>(chmod:*)", "mcp__supabase__list_projects", "mcp__supabase__execute_sql", "mcp__supabase__list_tables", "<PERSON><PERSON>(timeout:*)", "<PERSON><PERSON>(touch:*)", "<PERSON><PERSON>(playwright install:*)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(top:*)", "mcp__supabase__apply_migration", "<PERSON><PERSON>(cat:*)", "Bash(tmux ls:*)", "<PERSON><PERSON>(tmux capture-pane:*)", "<PERSON><PERSON>(tmux kill-session:*)", "Bash(tmux new:*)", "Bash(kill:*)", "<PERSON><PERSON>(pkill:*)", "mcp__supabase__list_extensions", "Bash(npm install)", "<PERSON><PERSON>(env)", "<PERSON><PERSON>(curl:*)", "Bash(identify:*)", "<PERSON><PERSON>(sed:*)", "Bash(xvfb-run:*)", "Bash(dpkg:*)", "Bash(systemctl status:*)", "<PERSON><PERSON>(sudo pkill:*)", "Bash(sudo rm:*)", "Bash(sudo dpkg:*)", "<PERSON><PERSON>(vncserver:*)", "Bash(echo $DISPLAY)", "Bash(systemctl:*)", "<PERSON><PERSON>(who:*)", "Bash(sudo ufw status:*)", "<PERSON><PERSON>(sudo:*)", "Bash(ss:*)", "<PERSON><PERSON>(playwright --version)", "<PERSON><PERSON>(echo:*)", "Bash(export DISPLAY=:1)", "Bash(DISPLAY=:1 python3 xhs.py)", "Bash(DISPLAY=:0 xvfb-run -a python3 xhs.py)", "Bash(export DISPLAY=:0)", "<PERSON><PERSON>(npx playwright install:*)", "Bash(node:*)", "Bash(gio set:*)", "<PERSON><PERSON>(./open-playwright.sh:*)", "Bash(DISPLAY=:0 npx playwright --version)", "Bash(DISPLAY=:0 npx playwright open chromium)", "Bash(DISPLAY=:0 node browser-launcher.js)"], "deny": []}}