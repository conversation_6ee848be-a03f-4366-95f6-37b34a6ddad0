import os
import json
import time
import logging
import logging.handlers
import atexit
import requests
import asyncio
import re
import zipfile
import subprocess
import signal
import sys
from concurrent.futures import ThreadPoolExecutor
import threading

from dotenv import load_dotenv
from collections import defaultdict
from datetime import datetime, timezone

from pyrogram import Client, filters
from pyrogram.types import (
    InlineKeyboardMarkup,
    InlineKeyboardButton,
    Message,
    InputMediaPhoto,
    InputMediaVideo,
)
from pyrogram.errors import RPCError

# Supabase Client (Python)
# Needs: pip install supabase
from supabase import create_client, Client as SupabaseClient

# ------------------- 日志管理：滚动日志 + 控制台 -------------------
def setup_logger():
    logger = logging.getLogger()
    logger.setLevel(logging.INFO)

    # 移除可能重复的默认处理器，避免重复打印
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)

    fmt = logging.Formatter('%(asctime)s [%(levelname)s] %(filename)s:%(lineno)d - %(message)s')

    # 1) 控制台输出
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(fmt)
    logger.addHandler(console_handler)

    # 2) 滚动文件输出，每个最大 10MB，保留 5 份 - 已禁用
    # file_handler = logging.handlers.RotatingFileHandler(
    #     'xhs_bot.log', maxBytes=10 * 1024 * 1024, backupCount=5, encoding='utf-8'
    # )
    # file_handler.setFormatter(fmt)
    # logger.addHandler(file_handler)
    
    # 3) 降低Pyrogram和其相关库的日志级别，减少冗余输出
    logging.getLogger("pyrogram").setLevel(logging.WARNING)
    logging.getLogger("pyrogram.session").setLevel(logging.WARNING)
    logging.getLogger("pyrogram.connection").setLevel(logging.WARNING)
    logging.getLogger("pyrogram.dispatcher").setLevel(logging.WARNING)
    logging.getLogger("pyrogram.auth").setLevel(logging.WARNING)
    
    return logger

# 全局 Logger
logger = setup_logger()

# ------------------- 环境变量处理 -------------------
def clean_env_var(value):
    """移除环境变量值中的引号"""
    if value and isinstance(value, str):
        # 如果值以引号开始和结束，则移除引号
        if (value.startswith('"') and value.endswith('"')) or (value.startswith("'") and value.endswith("'")):
            return value[1:-1]
    return value

def get_env(key, default=None):
    """获取环境变量并清理引号"""
    value = os.getenv(key, default)
    return clean_env_var(value)

# ------------------- 加载环境变量 -------------------
load_dotenv()

# Supabase Environment Variables
SUPABASE_URL = get_env("SUPABASE_URL")
SUPABASE_KEY = get_env("SUPABASE_KEY")

# TikHub API 环境变量（现在仅用于cookie获取，媒体获取已改用本地API）
TIKHUB_API_URL = get_env("XHS_TIKHUB_API_URL")  # 已弃用，保留以兼容
XHS_TIKHUB_API_TOKEN = get_env("XHS_TIKHUB_API_TOKEN")  # 用于cookie获取API认证

# 检查TikHub API配置（主要用于cookie自动更新）
if not XHS_TIKHUB_API_TOKEN:
    logger.warning("XHS_TIKHUB_API_TOKEN not provided in environment. Cookie auto-update will be disabled.")

supabase: SupabaseClient = None
if SUPABASE_URL and SUPABASE_KEY:
    try:
        supabase = create_client(SUPABASE_URL, SUPABASE_KEY)
        logger.info("Supabase client initialized successfully.")
    except Exception as e:
        logger.error(f"Failed to initialize Supabase client: {e}", exc_info=True)
        supabase = None # Ensure supabase is None if initialization fails
else:
    logger.warning("SUPABASE_URL or SUPABASE_KEY not provided in environment. Supabase integration will be disabled.")

# ------------------- 全局异常处理 -------------------
def handle_unhandled_exception(loop, context):
    """
    asyncio 的全局未处理异常捕获，可以记录更多报错信息
    """
    error = context.get("exception")
    if error is None:
        message = context["message"]
        logger.error(f"Unhandled error: {message}")
    else:
        logger.error("Unhandled exception", exc_info=error)

BOT_TOKEN = get_env("XHS_BOT_TOKEN")
API_ID = int(get_env("XHS_API_ID", "0"))
API_HASH = get_env("XHS_API_HASH")
XHS_API_URL = get_env("XHS_API_URL", "http://127.0.0.1:5556/xhs/detail")
TARGET_CHAT_ID = int(get_env("XHS_TARGET_CHAT_ID", "0"))
XHS_API_PATH = get_env("XHS_API_PATH", "/home/<USER>/XHS-Downloader/main.py")  # API程序文件
XHS_SETTINGS_PATH = get_env("XHS_SETTINGS_PATH", "/home/<USER>/XHS-Downloader/Volume/settings.json")
XHS_COOKIE_API = get_env("XHS_COOKIE_API", "https://beta.tikhub.io/api/v1/xiaohongshu/web/get_visitor_cookie")
# XHS_COOKIE_TOKEN = get_env("XHS_COOKIE_TOKEN") # 删除这行，使用XHS_TIKHUB_API_TOKEN

# 错误监控
ERROR_TRACKING = {
    "none_type_errors": 0,
    "last_error_time": 0,
    "last_restart_time": 0,
    "consecutive_errors": 0,
    "consecutive_empty_data_errors": 0,  # 新增：跟踪API返回空数据的连续错误
    "is_restarting": False,
}
ERROR_THRESHOLD = 3
ERROR_TIME_WINDOW = 60
RESTART_COOLDOWN = 300

BASE_DOWNLOAD_FOLDER = "download"
os.makedirs(BASE_DOWNLOAD_FOLDER, exist_ok=True)

SESSION_NAME = "xhs_bot_session"
bot = Client(
    SESSION_NAME,
    api_id=API_ID,
    api_hash=API_HASH,
    bot_token=BOT_TOKEN
)

# 单线程处理模式 - 移除并发限制
# SEM = asyncio.Semaphore(5)  # 已改为单线程处理
# UPLOAD_SEM = asyncio.Semaphore(1)  # 已改为单线程处理

# Telegram 消息发送锁，确保同一时间只有一个消息在发送
TELEGRAM_SEND_LOCK = asyncio.Lock()

# Telegram 相关限制
MAX_CAPTION_LEN = 1024
MAX_TEXT_LEN = 4096
MAX_MEDIA_GROUP_COUNT = 10
MAX_IMAGE_SEND_SIZE = int(10 * 1024 * 1024)  # 10MB
MAX_VIDEO_SEND_SIZE = 1900000000  # 1.9GB
VIDEO_THUMB_THRESHOLD = 50000000  # 50MB

user_incoming_messages = {}
MESSAGE_ACCUMULATE_TIMEOUT = 2.0  # 秒

@atexit.register
def cleanup_session():
    """
    程序退出时删除 Pyrogram 会话文件
    """
    try:
        session_file = f"{SESSION_NAME}.session"
        if os.path.exists(session_file):
            os.remove(session_file)
            logger.info(f"会话文件 {session_file} 已删除")
    except Exception as e:
        # 忽略清理错误，避免在退出时产生额外的错误信息
        pass

# ------------------- 重启逻辑：使用tmux管理服务 -------------------
def restart_xhs_api_service():
    """
    用 tmux 方式重启 XHS API 服务：
      1. 关闭名为 server 的旧会话
      2. 新开同名会话，并在其中启动
    """
    ERROR_TRACKING["is_restarting"] = True
    ERROR_TRACKING["last_restart_time"] = time.time()

    # 1) 关闭旧会话前先确保相关python进程已终止
    kill_cmd = f"pkill -f 'python3 main.py api'"
    subprocess.run(kill_cmd, shell=True)
    logger.info(f"已尝试终止旧的python进程: python3 {XHS_API_PATH} api")
    time.sleep(1)

    # 2) 关闭旧tmux会话
    session_name = "server"  # tmux会话名
    close_cmd = f"tmux kill-session -t {session_name} 2>/dev/null"
    subprocess.run(close_cmd, shell=True)
    logger.info(f"已尝试关闭旧的 tmux 会话: {session_name}")
    time.sleep(2)

    # 3) 新建 tmux 会话并启动 main.py api
    work_dir = "/home/<USER>/XHS-Downloader"
    start_cmd = f"tmux new-session -d -s {session_name} -c {work_dir} 'python3 main.py api'"
    subprocess.run(start_cmd, shell=True)
    logger.info(f"已新建 tmux 会话: {session_name}，正在后台运行 main.py api")
    
    # 等待固定时间，确保服务启动完成
    wait_time = 20  # 等待20秒让服务完全启动
    logger.info(f"等待服务启动中...({wait_time}秒)")
    time.sleep(wait_time)
    
    # 4) 检查tmux会话是否存在
    tmux_check_cmd = f"tmux has-session -t {session_name} 2>/dev/null"
    tmux_result = subprocess.run(tmux_check_cmd, shell=True)
    
    # 5) 检查python进程是否在运行
    process_check_cmd = f"ps aux | grep 'python3 main.py api' | grep -v grep"
    process_result = subprocess.run(process_check_cmd, shell=True, capture_output=True, text=True)
    
    tmux_exists = tmux_result.returncode == 0
    process_running = len(process_result.stdout.strip()) > 0
    
    if tmux_exists and process_running:
        logger.info(f"XHS API 服务已成功启动，tmux会话和python进程均已确认")
        ERROR_TRACKING["is_restarting"] = False
        return True
    else:
        error_msg = []
        if not tmux_exists:
            error_msg.append(f"未找到tmux会话 '{session_name}'")
        if not process_running:
            error_msg.append(f"未找到python进程 'python3 main.py api'")
            
        logger.error(f"XHS API 服务启动失败: {', '.join(error_msg)}，程序即将退出...")
        ERROR_TRACKING["is_restarting"] = False
        # 发送终止信号退出程序
        os.kill(os.getpid(), signal.SIGTERM)
        sys.exit(1)  # 确保程序退出
        return False

# ------------------- Cookie 更新 -------------------
def update_xhs_cookie():
    """
    从API获取最新的小红书cookie并更新到settings.json文件
    返回: (bool, str) -> (成功与否, 说明)
    """
    if not XHS_COOKIE_API or not XHS_TIKHUB_API_TOKEN:  # 使用XHS_TIKHUB_API_TOKEN替代XHS_COOKIE_TOKEN
        return False, "Cookie API URL或Token未配置，无法更新cookie"
    
    try:
        headers = {
            'accept': 'application/json',
            'Authorization': f'Bearer {XHS_TIKHUB_API_TOKEN}'  # 使用XHS_TIKHUB_API_TOKEN替代XHS_COOKIE_TOKEN
        }
        logger.info(f"获取小红书cookie: {XHS_COOKIE_API}")
        response = requests.get(XHS_COOKIE_API, headers=headers, timeout=30)
        response.raise_for_status()
        data = response.json()

        cookie_data = data.get('data', {}).get('data', {}).get('cookie')
        if not cookie_data:
            return False, "API返回数据中没有找到cookie字段"

        if not os.path.exists(XHS_SETTINGS_PATH):
            return False, f"设置文件不存在: {XHS_SETTINGS_PATH}"

        with open(XHS_SETTINGS_PATH, 'r', encoding='utf-8') as f:
            settings = json.load(f)

        old_cookie = settings.get('cookie', '')
        settings['cookie'] = cookie_data

        with open(XHS_SETTINGS_PATH, 'w', encoding='utf-8') as f:
            json.dump(settings, f, ensure_ascii=False, indent=4)

        logger.info("成功更新小红书cookie")
        return True, f"成功更新cookie。\n旧cookie: {old_cookie[:20]}...\n新cookie: {cookie_data[:20]}..."
    except Exception as e:
        logger.error(f"更新小红书cookie失败: {e}")
        return False, f"更新cookie失败: {e}"

# ------------------- 异常终止处理 -------------------
async def handle_fatal_error(remaining_links, error_message):
    """
    处理致命错误：发送所有待处理链接，然后退出程序
    """
    message = f"程序遇到致命错误，无法继续处理：{error_message}\n\n"
    message += f"以下 {len(remaining_links)} 个链接未处理完成：\n"
    
    for link in remaining_links:
        message += f"{link}\n"
    
    await safe_send_long_text(message)
    logger.error(f"程序因致命错误终止: {error_message}")
    
    # 确保消息发送后再退出
    await asyncio.sleep(2)
    os.kill(os.getpid(), signal.SIGTERM)
    sys.exit(1)

# ------------------- 数值转换辅助函数 -------------------
def convert_xhs_count(count_str: str) -> int:
    """
    将小红书作品数字段（如 "10+"、"1.5k"、"2w"）转换为整数。
    如果转换失败或输入无效，则返回 0。
    """
    if not count_str or not isinstance(count_str, str):
        return 0

    original_str = count_str
    count_str = count_str.lower().strip()
    
    num_part = ""
    for char in count_str:
        if char.isdigit() or char == '.':
            num_part += char
        else:
            # Stop at first non-numeric/non-dot character (like 'k', 'w', '+', or other text)
            break 

    if not num_part:
        return 0

    try:
        num = float(num_part)
    except ValueError:
        logger.debug(f"ValueError converting numeric part '{num_part}' from '{original_str}' to float.")
        return 0

    if 'k' in count_str: # Check original string for 'k' or 'w' to handle cases like "1.5k notes"
        num *= 1000
    elif 'w' in count_str: # Use 'elif' to avoid double multiplication if "kw" appears (unlikely)
        num *= 10000
    
    return int(num)

# ------------------- Supabase Upsert Functions -------------------
async def upsert_xhs_user_to_supabase(user_data_json: dict):
    if not supabase:
        # This case is logged at startup, so maybe just a debug log here or skip logging.
        # logger.debug("Supabase client not initialized. Skipping upsert_xhs_user_to_supabase.")
        return False

    if not user_data_json:
        logger.warning("user_data_json is empty for user upsert. Skipping.")
        return False

    user_id = user_data_json.get("作者ID")
    if not user_id:
        logger.warning("作者ID (user_id) not found in user_data_json. Skipping user upsert.")
        return False

    nickname = user_data_json.get("作者昵称")
    if not nickname:
        logger.warning(f"作者昵称 (nickname) not found for user_id {user_id}. Skipping user upsert.")
        return False # Nickname is NOT NULL

    # `images` is NOT NULL in DB. The note API JSON doesn't provide author avatar.
    # Use an empty string as a placeholder to satisfy NOT NULL constraint.
    images_url = user_data_json.get("作者头像", "") # Default to empty string if not found
    
    row = {
        "userid": user_id,
        "nickname": nickname,
        "images": images_url, 
        # created_at has DB default CURRENT_TIMESTAMP, let DB handle on insert.
        # updated_at has DB default CURRENT_TIMESTAMP, explicitly set for upsert.
        "updated_at": datetime.now(timezone.utc).isoformat(),
        # Other fields (followed, fstatus, red_official_verify_type, surveillance) use DB defaults.
    }

    # Ensure essential non-nullable fields are present before upserting
    if not all([row["userid"], row["nickname"], row["images"] is not None]): # images can be ""
        logger.error(f"Critical data (userid, nickname, images) missing or None before user upsert. UserID: {user_id}. Row: {row}")
        return False

    try:
        # logger.debug(f"Attempting to upsert user to Supabase: {row}")
        response = await asyncio.to_thread(supabase.table("xhs_user").upsert(row).execute)
        
        # supabase-py v2 execute() raises an error on failure, or response.data is populated on success.
        # If no error is raised, assume success for now.
        # More robust error checking would inspect response.error if it exists or rely on exceptions.
        logger.info(f"Successfully upserted user_id {user_id} to Supabase. Response data: {getattr(response, 'data', 'N/A')}")
        return True
    except Exception as e:
        logger.error(f"Error upserting user_id {user_id} to Supabase: {e}", exc_info=True)
        logger.debug(f"Failed row data for user_id {user_id}: {row}")
        return False

async def upsert_xhs_note_to_supabase(note_data_json: dict, file_ids_str: str | None = None, short_link: str | None = None, original_link: str | None = None):
    if not supabase:
        # logger.debug("Supabase client not initialized. Skipping upsert_xhs_note_to_supabase.")
        return False

    if not note_data_json:
        logger.warning("note_data_json is empty for note upsert. Skipping.")
        return False

    note_id = note_data_json.get("作品ID")
    if not note_id:
        logger.warning("作品ID (note_id) not found in note_data_json. Skipping note upsert.")
        return False

    row = {
        "id": note_id,
        "title": note_data_json.get("作品标题"),
        "author_id": note_data_json.get("作者ID"),
        "author_name": note_data_json.get("作者昵称"),
        "author_link": note_data_json.get("作者链接"),
        "note_link": original_link if original_link else note_data_json.get("作品链接"),  # 优先使用原始链接
        "content": note_data_json.get("作品描述"),
        "likes_count": convert_xhs_count(note_data_json.get("点赞数量")),
        "comments_count": convert_xhs_count(note_data_json.get("评论数量")),
        "collect_count": convert_xhs_count(note_data_json.get("收藏数量")),
        "share_count": convert_xhs_count(note_data_json.get("分享数量")),
        "publish_time": note_data_json.get("发布时间"), # Stored as TEXT, format YYYY-MM-DD_HH:MM:SS
        "topic": note_data_json.get("作品标签"),
        "location": note_data_json.get("地理位置"), # Assuming this key if location data exists
        "note_type": note_data_json.get("作品类型"),  # 添加作品类型
        "last_update_time": note_data_json.get("最后更新时间"),  # 添加最后更新时间
        "timestamp": note_data_json.get("时间戳"),  # 添加时间戳
        "created_at": datetime.now(timezone.utc).isoformat(), # Table DDL has no default for this
        "file_id": file_ids_str,
        "short_link": short_link,  # 添加短链接字段
    }

    # Remove keys with None values to allow DB defaults to apply or keep existing values if not provided during update.
    # However, for fields that are part of the primary key or have NOT NULL constraints without DB defaults,
    # they must be present. 'id' is PK. 'created_at' we are providing.
    row_cleaned = {k: v for k, v in row.items() if v is not None}
    
    if "id" not in row_cleaned: # Should not happen if note_id was present
         logger.error(f"Critical error: 'id' became None for note_id {note_id} during cleaning. Original ID: {note_data_json.get('作品ID')}")
         return False
    if "created_at" not in row_cleaned: # Should not happen
         logger.error(f"Critical error: 'created_at' became None for note_id {note_id}.")
         return False

    try:
        # logger.debug(f"Attempting to upsert note to Supabase: {row_cleaned}")
        response = await asyncio.to_thread(supabase.table("xhs_notes").upsert(row_cleaned).execute)
        
        logger.info(f"Successfully upserted note_id {note_id} to Supabase. Response data: {getattr(response, 'data', 'N/A')}")
        return True
    except Exception as e:
        logger.error(f"Error upserting note_id {note_id} to Supabase: {e}", exc_info=True)
        logger.debug(f"Failed row data for note_id {note_id}: {row_cleaned}")
        return False

# ------------------- Telegram File ID Extractor -------------------
def extract_file_id_from_message(message: Message) -> str | None:
    """Extracts file_id from a Pyrogram Message object."""
    if not message:
        return None
    if message.photo:
        return message.photo.file_id
    elif message.video:
        return message.video.file_id
    elif message.document:
        return message.document.file_id
    elif message.audio:
        return message.audio.file_id
    elif message.sticker:
        return message.sticker.file_id
    elif message.animation: # GIF sent as animation
        return message.animation.file_id
    # Add other types like voice, video_note if needed
    logger.debug(f"Message type {message.media} not handled for file_id extraction or no media.")
    return None

def detect_file_type_from_id(file_id: str) -> str | None:
    """
    Detects the type of a Telegram file based on its file_id prefix.
    Note: Prefixes can vary slightly based on bot type (Bot API vs UserBot) and age of file_id.
    This function covers common patterns.
    """
    if not file_id or not isinstance(file_id, str):
        return None

    # User's provided prefixes from JS
    if file_id.startswith('AgACAg'):  # Photo
        return 'photo'
    elif file_id.startswith('BAACAg'):  # Video
        return 'video'
    elif file_id.startswith('BQACAg') or file_id.startswith('BQADAg'):  # Document
        return 'document'
    elif file_id.startswith('CQACAg') or file_id.startswith('CQADAgU'):  # Audio
        return 'audio'
    elif file_id.startswith('AwADAg'):  # Voice
        return 'voice'
    elif file_id.startswith('CAADAg') or file_id.startswith('CAACAg'): # Sticker (CAADAg might be older or different context)
        return 'sticker'
    elif file_id.startswith('CgADAg'):  # Animation (GIF)
        return 'animation'
    elif file_id.startswith('DQADAg'):  # Video Note
        return 'video_note'

    # Common Pyrogram (UserClient) specific patterns often include 'UAA' or similar.
    # These checks can catch more specific UserClient file_ids if the above are too general.
    # Order might matter if prefixes overlap; more specific checks could go first if needed.
    if 'AgACAgUAA' in file_id or 'AgADAgUAA' in file_id: return 'photo'
    if 'BAACAgUAA' in file_id: return 'video'
    if 'BQACAgUAA' in file_id: return 'document' # Often seen for documents from UserClients
    if 'CQACAgUAA' in file_id: return 'audio'
    if 'CgACAgUAA' in file_id: return 'animation'
    
    # If still no match, log a warning. The Telegram library will ultimately validate.
    logger.warning(f"Could not determine file type for file_id: {file_id} based on common prefixes. Telegram will validate.")
    return 'unknown' # Return 'unknown' instead of None, as it might still be a valid ID of an uncategorized type.

# ------------------- 错误检测与重启处理 -------------------
def check_and_handle_api_errors(error_message):
    """
    检查并处理API相关错误，一旦确定是Cookie/服务异常，立刻更新cookie并重启
    """
    current_time = time.time()
    # 将cooldown_passed移到函数开头，避免UnboundLocalError
    cooldown_passed = (current_time - ERROR_TRACKING["last_restart_time"] > RESTART_COOLDOWN)
    
    if "'NoneType' object has no attribute 'get'" in error_message:
        ERROR_TRACKING["none_type_errors"] += 1
        ERROR_TRACKING["consecutive_errors"] += 1
        # 确认需要重启
        if cooldown_passed and not ERROR_TRACKING["is_restarting"] and ERROR_TRACKING["consecutive_errors"] >= ERROR_THRESHOLD:
            logger.warning(f"检测到连续{ERROR_TRACKING['consecutive_errors']}次NoneType错误，立即尝试更新cookie并重启API服务")
            
            # 尝试更新cookie，最多重试5次
            cookie_updated = False
            for attempt in range(5):  # 增加到5次尝试
                success, msg = update_xhs_cookie()
                if success:
                    cookie_updated = True
                    asyncio.create_task(
                        safe_send_long_text(f"API服务出现问题，已更新cookie（尝试{attempt+1}次成功），开始重启服务...\n{msg}")
                    )
                    logger.info(f"Cookie更新成功（尝试{attempt+1}次）: {msg}")
                    break
                else:
                    logger.error(f"Cookie更新失败（尝试{attempt+1}次）: {msg}")
                    if attempt < 4:  # 如果不是最后一次尝试
                        logger.info(f"等待5秒后重试更新cookie...")
                        time.sleep(5)
            
            # 即使cookie更新失败也尝试重启服务
            if not cookie_updated:
                asyncio.create_task(
                    safe_send_long_text(f"API服务出现问题，cookie更新失败(尝试5次)，仍将尝试重启服务...")
                )
                logger.warning("所有cookie更新尝试均失败，仍将尝试重启服务")
            
            restart_success = restart_xhs_api_service()
            if restart_success:
                ERROR_TRACKING["consecutive_errors"] = 0  # 重置连续错误计数
                ERROR_TRACKING["consecutive_empty_data_errors"] = 0  # 重置空数据错误计数
                asyncio.create_task(safe_send_long_text("XHS API服务已成功重启"))
                return True, False  # 第一个返回值：已处理错误，第二个返回值：是否致命错误
            else:
                asyncio.create_task(safe_send_long_text("XHS API服务重启失败，程序即将退出"))
                return True, True  # 服务无法恢复，这是致命错误
    elif "API返回数据为空" in error_message or "无 'data' 字段" in error_message:
        ERROR_TRACKING["consecutive_empty_data_errors"] += 1
        # 当连续出现3次或以上API返回空数据的情况，也尝试更新cookie并重启
        if ERROR_TRACKING["consecutive_empty_data_errors"] >= ERROR_THRESHOLD and cooldown_passed and not ERROR_TRACKING["is_restarting"]:
            logger.warning(f"检测到连续{ERROR_TRACKING['consecutive_empty_data_errors']}次API返回空数据，立即尝试更新cookie并重启API服务")
            # 尝试更新cookie，逻辑与上面相同
            cookie_updated = False
            for attempt in range(5):  # 增加到5次尝试
                success, msg = update_xhs_cookie()
                if success:
                    cookie_updated = True
                    asyncio.create_task(
                        safe_send_long_text(f"API连续返回空数据，已更新cookie（尝试{attempt+1}次成功），开始重启服务...\n{msg}")
                    )
                    logger.info(f"Cookie更新成功（尝试{attempt+1}次）: {msg}")
                    break
                else:
                    logger.error(f"Cookie更新失败（尝试{attempt+1}次）: {msg}")
                    if attempt < 4:  # 如果不是最后一次尝试
                        logger.info(f"等待5秒后重试更新cookie...")
                        time.sleep(5)
            
            if not cookie_updated:
                asyncio.create_task(
                    safe_send_long_text(f"API连续返回空数据，cookie更新失败(尝试5次)，仍将尝试重启服务...")
                )
            
            restart_success = restart_xhs_api_service()
            if restart_success:
                ERROR_TRACKING["consecutive_errors"] = 0  # 重置连续错误计数
                ERROR_TRACKING["consecutive_empty_data_errors"] = 0  # 重置空数据错误计数
                asyncio.create_task(safe_send_long_text("XHS API服务已成功重启"))
                return True, False
            else:
                asyncio.create_task(safe_send_long_text("XHS API服务重启失败，程序即将退出"))
                return True, True
    return False, False

# ------------------- 消息累积处理 -------------------
def schedule_handle_user_texts(chat_id: int):
    old_task = user_incoming_messages[chat_id].get("timer_task", None)
    if old_task:
        old_task.cancel()
    async def delayed_process():
        await asyncio.sleep(MESSAGE_ACCUMULATE_TIMEOUT)
        await finalize_user_texts(chat_id)
    new_task = asyncio.create_task(delayed_process())
    user_incoming_messages[chat_id]["timer_task"] = new_task

async def finalize_user_texts(chat_id: int):
    data = user_incoming_messages.get(chat_id, None)
    if not data:
        return
    messages = data["messages"]
    user_incoming_messages.pop(chat_id, None)
    if not messages:
        return
    all_text_parts = [m.text.strip() for m in messages if m.text]
    merged_text = "\n".join(all_text_parts)

    # 删除用户发来的原始消息
    try:
        msg_ids = [m.id for m in messages]
        async with TELEGRAM_SEND_LOCK:
            await bot.delete_messages(chat_id=chat_id, message_ids=msg_ids)
    except Exception as e:
        logger.error(f"删除用户原消息出错: {e}")

    if not merged_text:
        return

    # 提取链接
    xhs_links = extract_xhs_links(merged_text)
    if not xhs_links:
        truncated = (merged_text[:4000] + "...") if len(merged_text) > 4000 else merged_text
        await safe_send_long_text(f"未检测到小红书链接。\n输入内容：\n{truncated}")
        return

    link_count = len(xhs_links)
    await ephemeral_send_text(f"已收到 {link_count} 条链接，开始处理，请稍候...", delay=30)

    # 单线程顺序处理，避免API服务重启时的并发问题
    results = await process_links_batch(xhs_links, file_lines=None, from_json=False)

    # 收集失败的链接
    failed_links = [link_str for link_str, success in results if not success]
    
    # 如果有失败的链接，且失败原因可能是cookie问题，尝试更新cookie后重试
    if failed_links and ERROR_TRACKING.get("consecutive_empty_data_errors", 0) >= ERROR_THRESHOLD:
        logger.info(f"检测到 {len(failed_links)} 个链接处理失败，尝试更新cookie后重试...")
        await ephemeral_send_text(f"检测到 {len(failed_links)} 个链接失败，正在更新cookie并重试...", delay=10)
        
        # 更新cookie并重启API
        cookie_success, cookie_msg = update_xhs_cookie()
        if cookie_success:
            logger.info(f"Cookie更新成功，准备重启API服务: {cookie_msg}")
            service_ok = restart_xhs_api_service()
            if service_ok:
                logger.info("API服务重启成功，开始重试失败的链接")
                await ephemeral_send_text(f"Cookie已更新，正在重试 {len(failed_links)} 个失败的链接...", delay=10)
                
                # 重置错误计数
                ERROR_TRACKING["consecutive_errors"] = 0
                ERROR_TRACKING["consecutive_empty_data_errors"] = 0
                
                # 重试失败的链接
                retry_results = await process_links_batch(failed_links, file_lines=None, from_json=False)
                
                # 更新结果
                retry_dict = dict(retry_results)
                for i, (link_str, original_success) in enumerate(results):
                    if link_str in retry_dict:
                        results[i] = (link_str, retry_dict[link_str])
            else:
                logger.error("API服务重启失败，无法重试")
        else:
            logger.warning(f"Cookie更新失败: {cookie_msg}")

    # 收集重试后仍然失败的链接，使用TikHub API检查
    final_failed_links = [link_str for link_str, success in results if not success and success != "deleted"]
    tikhub_deleted_links = []
    
    if final_failed_links and XHS_TIKHUB_API_TOKEN:
        logger.info(f"有 {len(final_failed_links)} 个链接重试后仍失败，使用TikHub API进行最终检查...")
        await ephemeral_send_text(f"正在使用TikHub API检查 {len(final_failed_links)} 个失败链接的状态...", delay=10)
        
        for link_str in final_failed_links:
            # 从链接中提取作品ID
            note_id = None
            import re
            patterns = [
                r'/explore/([a-f0-9]{24})',
                r'/discovery/item/([a-f0-9]{24})',
                r'note_id=([a-f0-9]{24})',
                r'([a-f0-9]{24})',
            ]
            
            for pattern in patterns:
                match = re.search(pattern, link_str)
                if match:
                    note_id = match.group(1)
                    break
            
            if note_id:
                # 使用TikHub API检查
                tikhub_result = await check_note_deleted_via_tikhub(note_id)
                
                if tikhub_result['deleted']:
                    # 更新结果为已删除
                    for i, (l, s) in enumerate(results):
                        if l == link_str:
                            results[i] = (link_str, "deleted")
                            tikhub_deleted_links.append(link_str)
                            logger.info(f"TikHub API确认作品已删除: {link_str}")
                            break
                elif tikhub_result['exists'] and tikhub_result['media_urls']:
                    # 作品存在且有媒体资源，尝试使用TikHub的资源下载
                    logger.info(f"TikHub API确认作品存在，尝试使用TikHub资源下载: {link_str}")
                    
                    # 构造一个简化的xhs_data格式用于下载
                    tikhub_xhs_data = {
                        "code": 0,
                        "data": {
                            "作品ID": note_id,
                            "作品链接": link_str,
                            "媒体类型": tikhub_result['type'],
                            "媒体URLs": tikhub_result['media_urls'],
                            "来源": "TikHub API"
                        }
                    }
                    
                    # 尝试处理这个作品
                    try:
                        success = await process_tikhub_media(link_str, tikhub_xhs_data, tikhub_result)
                        if success:
                            # 更新结果为成功
                            for i, (l, s) in enumerate(results):
                                if l == link_str:
                                    results[i] = (link_str, True)
                                    logger.info(f"TikHub API资源下载成功: {link_str}")
                                    break
                        else:
                            logger.warning(f"TikHub API资源下载失败: {link_str}")
                    except Exception as e:
                        logger.error(f"处理TikHub资源时出错: {e}")
                        
                elif tikhub_result['exists']:
                    logger.info(f"TikHub API确认作品存在但无媒体资源: {link_str}")
                else:
                    logger.warning(f"TikHub API无法确认作品状态: {link_str}")
    
    # 重新统计结果
    if len(results) > 5:
        total_count = len(results)
        # 统计不同状态的数量
        success_count = sum(1 for r in results if r[1] == True)
        deleted_count = sum(1 for r in results if r[1] == "deleted")
        fail_count = sum(1 for r in results if r[1] == False)
        
        summary_text = f"本次共处理 {total_count} 个链接：\n"
        summary_text += f"✅ 成功: {success_count} 个\n"
        if deleted_count > 0:
            summary_text += f"⚠️ 已删除: {deleted_count} 个\n"
        summary_text += f"❌ 失败: {fail_count} 个"
        
        # 如果有重试，添加说明
        if failed_links and ERROR_TRACKING.get("consecutive_empty_data_errors", 0) >= ERROR_THRESHOLD:
            retry_success_count = sum(1 for link_str, success in results if link_str in failed_links and success == True)
            if retry_success_count > 0:
                summary_text += f"\n（其中 {retry_success_count} 个链接在更新cookie后重试成功）"
        
        # 如果有通过TikHub确认删除的，添加说明
        if tikhub_deleted_links:
            summary_text += f"\n（TikHub API确认 {len(tikhub_deleted_links)} 个作品已删除）"
        
        summary_text += "\n"
        
        # 显示已删除的链接
        deleted_links = [link_str for link_str, status in results if status == "deleted"]
        if deleted_links:
            summary_text += "\n以下作品已被删除：\n"
            for link_str in deleted_links:
                summary_text += f"🗑️ {link_str}\n"
        
        # 最终还是失败的链接
        final_failed = [link_str for link_str, status in results if status == False]
        if final_failed:
            summary_text += "\n以下链接处理失败：\n"
            for link_str in final_failed:
                summary_text += f"❌ {link_str}\n"
        
        await safe_send_long_text(summary_text)

# ------------------- 链接格式转换 -------------------
def convert_image_format(url: str) -> str:
    """
    转换图片URL的格式，将PNG转为JPEG，或将JPEG转为PNG
    只替换format部分，保留其他所有参数
    
    Args:
        url: 原始图片URL
    
    Returns:
        str: 转换格式后的URL
    """
    # 精确替换格式，保留其他所有参数
    if "format/png" in url:
        return url.replace("format/png", "format/jpeg")
    elif "format/jpeg" in url:
        return url.replace("format/jpeg", "format/png")
    elif "format/webp" in url:  # 处理可能存在的webp链接
        return url.replace("format/webp", "format/jpeg")
    elif "imageView2" in url and "format/" not in url:
        # 对于没有指定格式的imageView2 URL，添加format参数
        if "&" in url:
            # 在第一个&之前插入format参数
            parts = url.split("&", 1)
            return f"{parts[0]}/format/jpeg&{parts[1]}"
        elif "?" in url:
            # 没有&但有?，在URL末尾添加format
            return f"{url}/format/jpeg"
        else:
            # 既没有&也没有?，添加?和format
            return f"{url}?imageView2/2/w/1080/format/jpeg"
    
    # 如果无法识别格式或不需要修改，返回原URL
    return url

async def download_original(url: str, folder_path: str, work_id: str, author_nickname: str, index: int, is_video_work: bool = False) -> tuple[str, str, str]:
    """
    下载原始媒体文件，对于图片会下载PNG和JPEG两个版本
    
    Args:
        url: 媒体URL
        folder_path: 下载目录
        work_id: 作品ID
        author_nickname: 作者昵称
        index: 媒体索引
        is_video_work: 是否为视频作品（用于决定是否使用多线程下载）
    
    Returns:
        tuple[str, str, str]: (png_filepath, file_type, jpeg_filepath)
                              file_type为'image'或'video'；
                              对于图片，返回PNG版本的路径和JPEG版本的路径（如果成功）
    """
    try:
        # 判断是否为图片URL
        is_image = "imageView2" in url or ".jpg" in url.lower() or ".png" in url.lower() or ".webp" in url.lower() or "sns-img" in url.lower() or "sns-na" in url.lower() or "ci.xiaohongshu.com" in url.lower()

        if is_image:
            logger.info(f"📥 开始下载图片 #{index} - 原始URL: {url}")

            # 1. Derive PNG download URL - use original image without any parameters
            png_target_url = url
            # Remove imageView2 and all parameters to get original image
            if "?" in url and "imageView2" in url:
                # Get base URL without query parameters
                png_target_url = url.split("?")[0]
            elif "#" in url:  # Handle any fragment identifiers
                png_target_url = url.split("#")[0]
            
            # 2. Derive JPEG download URL (instead of WebP)
            jpeg_target_url = url
            if "format/png" in url: # Original URL is explicitly PNG
                jpeg_target_url = url.replace("format/png", "format/jpeg", 1)
            elif "format/webp" in url: # Original URL is WebP
                jpeg_target_url = url.replace("format/webp", "format/jpeg", 1)
            # Original URL is not PNG/WebP, and needs JPEG version (could be no format or other format)
            elif "imageView2" in url and "format/jpeg" not in url: 
                query_string_exists = "?" in url
                query_part_of_url = url.split("?", 1)[1] if query_string_exists else ""
                
                # Optimization for xhscdn.com if no format is specified in the query
                if "xhscdn.com/" in url and "format/" not in query_part_of_url:
                    base_url_part = url.split('?')[0]
                    jpeg_target_url = f"{base_url_part}?imageView2/2/w/1080/format/jpeg/q/85"
                else: # General modification for imageView2 URLs to add /format/jpeg
                    match = re.search(r'(imageView2(?:(?!/q/).)*?)(/q/[^&?]*)?', url)
                    if match:
                        base_imageView_part = match.group(1)
                        q_param_part = match.group(2) if match.group(2) else ""
                        # Add /format/jpeg if not already effectively there and not dealing with format/png
                        if not base_imageView_part.endswith("/format/jpeg") and "format/png" not in base_imageView_part:
                             new_imageView_section = f"{base_imageView_part}/format/jpeg{q_param_part}"
                             jpeg_target_url = url.replace(match.group(0), new_imageView_section, 1)
                        # If url already has a different format, it will be replaced with JPEG

            logger.info(f"Original media URL: {url}")
            logger.info(f"Derived PNG URL for download: {png_target_url}")
            logger.info(f"Derived JPEG URL for download: {jpeg_target_url}")

            png_filepath = ""
            png_file_data, png_content_type = await asyncio.to_thread(download_file, png_target_url)
            if png_file_data:
                # Determine file extension based on content-type
                if 'image/png' in png_content_type:
                    ext = 'png'
                elif 'image/jpeg' in png_content_type or 'image/jpg' in png_content_type:
                    ext = 'jpg'
                elif 'image/webp' in png_content_type:
                    ext = 'webp'
                elif 'image/gif' in png_content_type:
                    ext = 'gif'
                else:
                    # Fallback to magic number detection
                    if png_file_data.startswith(b'\x89PNG\r\n\x1a\n'):
                        ext = 'png'
                    elif png_file_data.startswith(b'\xff\xd8\xff'):
                        ext = 'jpg'
                    elif png_file_data.startswith(b'RIFF') and png_file_data[8:12] == b'WEBP':
                        ext = 'webp'
                    elif png_file_data.startswith(b'GIF87a') or png_file_data.startswith(b'GIF89a'):
                        ext = 'gif'
                    else:
                        ext = 'png'  # Default to png
                
                safe_nickname = re.sub(r'[\\/*?:"<>|]', "_", author_nickname)[:20]
                png_filename = f"xhs_{work_id}_{safe_nickname}_{index}.{ext}"
                png_filepath = os.path.join(folder_path, png_filename)
                with open(png_filepath, "wb") as f:
                    f.write(png_file_data)
                logger.info(f"Saved original image (content-type: {png_content_type}) to {png_filepath} (size: {os.path.getsize(png_filepath)}) from {png_target_url}")
            else:
                logger.warning(f"Failed to download original image data from {png_target_url}")

            jpeg_file_data = None
            jpeg_filepath = ""
            if jpeg_target_url == png_target_url and png_file_data:
                jpeg_file_data = png_file_data 
                logger.info(f"JPEG URL is same as PNG URL ({jpeg_target_url}), reusing downloaded PNG data for JPEG.")
            elif jpeg_target_url: 
                logger.info(f"Attempting to download JPEG version from: {jpeg_target_url}")
                jpeg_file_data, _ = await asyncio.to_thread(download_file, jpeg_target_url)
            
            if jpeg_file_data:
                safe_nickname = re.sub(r'[\\/*?:"<>|]', "_", author_nickname)[:20]
                jpeg_filename = f"xhs_{work_id}_{safe_nickname}_{index}.jpeg"
                jpeg_filepath = os.path.join(folder_path, jpeg_filename)
                with open(jpeg_filepath, "wb") as f:
                    f.write(jpeg_file_data)
                logger.info(f"Saved JPEG version to {jpeg_filepath} (size: {os.path.getsize(jpeg_filepath)}) from {jpeg_target_url}")
            else:
                if not (jpeg_target_url == png_target_url and png_file_data): 
                    logger.warning(f"Failed to download JPEG version from {jpeg_target_url}")
            
            # 修改：总是返回三元组，即使PNG和JPEG都失败
            if not png_filepath and not jpeg_filepath: # Total failure for this item
                 return "", "image", "" 
            
            # 即使PNG下载失败但JPEG成功，也返回正确的三元组
            return (png_filepath, "image", jpeg_filepath)

        else: # Video or other
            logger.info(f"🎬 开始下载视频 #{index} - 原始URL: {url}")
            # 如果是视频作品的视频，使用多线程下载
            if is_video_work:
                logger.info(f"检测到视频作品，尝试多线程下载")
                file_data, content_type = await asyncio.to_thread(download_video_multithread, url)
            else:
                # 动图或其他类型，使用普通下载
                logger.info(f"非视频作品（可能是动图），使用单线程下载")
                file_data, content_type = await asyncio.to_thread(download_file, url)
            if not file_data:
                logger.warning(f"Failed to download video/other data from {url}")
                return "", "video", ""

            try:
                # For videos, save with a proper extension
                ext = "mp4"  # Default to mp4 for videos
                safe_nickname = re.sub(r'[\\/*?:"<>|]', "_", author_nickname)[:20]
                video_filename = f"xhs_{work_id}_{safe_nickname}_{index}.{ext}"
                video_filepath = os.path.join(folder_path, video_filename)
                with open(video_filepath, "wb") as f:
                    f.write(file_data)
                logger.info(f"Saved video to {video_filepath} (size: {os.path.getsize(video_filepath)}) from {url}")
                return (video_filepath, "video", "")  # Return empty string for jpeg_filepath 
            except Exception as e:
                logger.error(f"保存视频文件 {url} 失败: {e}")
                return "", "video", ""

    except Exception as e:
        logger.error(f"下载 {url} 失败: {e}")
        # 返回一个带有类型信息的三元组，以便上层函数能正确处理
        if "imageView2" in url or any(x in url.lower() for x in [".jpg", ".jpeg", ".png", ".webp", "sns-img", "sns-na", "ci.xiaohongshu.com"]):
            return "", "image", ""
        else:
            return "", "video", ""

async def resend_from_supabase(note_db_data: dict, work_link_for_button: str, author_link_for_button: str) -> bool:
    """
    Resends a previously processed XHS note using file_ids stored in Supabase.
    Constructs captions and keyboard similar to the original processing flow.
    Does not fall back to reprocessing if sending via file_id fails.
    """
    note_id_for_logs = note_db_data.get("id", "UnknownID")
    logger.info(f"Attempting to resend note {note_id_for_logs} using data from Supabase.")
    
    file_ids_str = note_db_data.get("file_id")
    if not file_ids_str:
        error_msg_user = f"错误：数据库中存在笔记 {note_id_for_logs}，但没有有效的 file_id 记录，无法重新发送。"
        await safe_send_long_text(error_msg_user)
        logger.error(f"No file_ids found in Supabase for note {note_id_for_logs} for resending.")
        return False

    all_file_ids = [fid.strip() for fid in file_ids_str.split(';') if fid.strip()] # Ensure IDs are stripped
    if not all_file_ids:
        error_msg_user = f"错误：数据库中笔记 {note_id_for_logs} 的 file_id 记录解析后为空，无法重新发送。"
        await safe_send_long_text(error_msg_user)
        logger.error(f"File_ids string '{file_ids_str}' for note {note_id_for_logs} is empty after split and strip.")
        return False

    # Reconstruct data similar to API's data_field for caption generation
    # Ensure all values used for caption are strings if that's what original caption logic expects.
    caption_data_for_resend = {
        "收藏数量": str(note_db_data.get("collect_count", 0)),
        "评论数量": str(note_db_data.get("comments_count", 0)),
        "分享数量": str(note_db_data.get("share_count", 0)),
        "点赞数量": str(note_db_data.get("likes_count", 0)),
        "作品标签": str(note_db_data.get("topic", "")) if note_db_data.get("topic") is not None else "",
        "作品ID": str(note_db_data.get("id", "")) if note_db_data.get("id") is not None else "",
        "作品描述": str(note_db_data.get("content", "")) if note_db_data.get("content") is not None else "",
        "作品类型": str(note_db_data.get("note_type", "")) if note_db_data.get("note_type") is not None else "",
        "发布时间": str(note_db_data.get("publish_time", "")) if note_db_data.get("publish_time") is not None else "",
        "最后更新时间": str(note_db_data.get("last_update_time", "")) if note_db_data.get("last_update_time") is not None else "",
        "时间戳": str(note_db_data.get("timestamp", "")) if note_db_data.get("timestamp") is not None else "",
        "作者昵称": str(note_db_data.get("author_name", "")) if note_db_data.get("author_name") is not None else "",
        "作者ID": str(note_db_data.get("author_id", "")) if note_db_data.get("author_id") is not None else "",
    }
    # Remove keys with empty string values that were originally None, to match original caption logic more closely
    caption_data_for_resend = {k: v for k, v in caption_data_for_resend.items() if v or k in ["点赞数量", "评论数量", "收藏数量", "分享数量"]} # Keep count fields even if 0

    exclude_fields_for_caption = {"作品链接", "作者链接", "下载地址", "动图地址"} 
    info_lines = []
    for k, v_str in caption_data_for_resend.items():
        if k in exclude_fields_for_caption: 
            continue
        # The original loop skips if `not v`. Here v_str is already string. Empty strings were filtered above mostly.
        # So an explicit check for empty string might be good if some fields could be legitimately empty strings vs None.
        if not v_str and k not in ["点赞数量", "评论数量", "收藏数量", "分享数量"]: # Allow zero counts to be displayed
             continue
        info_lines.append(f"{k}: {v_str}")
        
    full_caption_text_resend = "\n".join(info_lines)
    short_cap_resend = safe_truncate_caption(full_caption_text_resend, MAX_CAPTION_LEN)

    # 添加重新处理和保存JSON按钮，只传递note_id（callback_data有64字节限制）
    # 获取作者ID用于订阅按钮
    author_id_for_sub = note_db_data.get("author_id", "")
    keyboard_resend = InlineKeyboardMarkup([
        [
            InlineKeyboardButton("打开作品", url=work_link_for_button),
            InlineKeyboardButton("作者主页", url=author_link_for_button)
        ],
        [
            InlineKeyboardButton("🔄 重新处理", callback_data=f"reprocess_{note_id_for_logs}"),
            InlineKeyboardButton("💾 保存JSON", callback_data=f"savejson_{note_id_for_logs}")
        ],
        [
            InlineKeyboardButton("⭐ 订阅作者", callback_data=f"sub_{author_id_for_sub}")
        ]
    ])

    media_items_input_group = []
    zip_file_id_to_send = None
    media_candidate_ids = all_file_ids # Assume all are media initially

    if all_file_ids:
        last_fid = all_file_ids[-1]
        if detect_file_type_from_id(last_fid) == 'document':
            zip_file_id_to_send = last_fid
            logger.info(f"Resend: Identified potential ZIP file_id (last document in list): {zip_file_id_to_send}")
            media_candidate_ids = all_file_ids[:-1] # Process remaining IDs for media group
        else:
            logger.info(f"Resend: Last file_id {last_fid} (type: {detect_file_type_from_id(last_fid)}) is not 'document'. Assuming no separate ZIP file_id to send by position.")

    for fid in media_candidate_ids:
        file_type = detect_file_type_from_id(fid)
        if file_type == 'photo':
            media_items_input_group.append(InputMediaPhoto(media=fid))
        elif file_type == 'video':
            media_items_input_group.append(InputMediaVideo(media=fid))
        elif file_type == 'document':
             logger.warning(f"Resend: Found document file_id {fid} in media candidates. It might be an individually sent file. For now, only photo/video are added to media group. This document will be skipped unless it was the identified ZIP.")
        elif file_type == 'unknown' or file_type is None:
            logger.warning(f"Resend: File_id {fid} has unknown type or type detection failed. Skipping for media group.")
        else: # audio, sticker, animation, voice, video_note etc.
            logger.info(f"Resend: File_id {fid} is of type '{file_type}'. Not adding to photo/video media group.")

    sent_media_successfully = True
    if media_items_input_group:
        if len(media_items_input_group) > MAX_MEDIA_GROUP_COUNT:
            logger.warning(f"Resend: Number of media items ({len(media_items_input_group)}) exceeds max group count {MAX_MEDIA_GROUP_COUNT}. Truncating.")
            media_items_input_group = media_items_input_group[:MAX_MEDIA_GROUP_COUNT]
        
        if media_items_input_group: # Check again after potential truncation
            # Set caption for the first item
            try: # Pyrogram InputMedia types might not allow direct caption assignment after init for file_id based media
                  # Instead, send with caption on the group or first message for single items.
                  # For send_media_group, caption is on the first item of the list of InputMedia passed.
                first_item_with_caption = media_items_input_group[0]
                # 移除媒体组的说明文字，ZIP文件已包含完整信息
                # if hasattr(first_item_with_caption, 'caption'): # Check if caption attribute exists
                #     first_item_with_caption.caption = short_cap_resend
                # else: # If not, need to recreate with caption (less ideal)
                #     logger.warning("Resend: First media item does not have a direct caption attribute. Captioning might be an issue for media group via file_id.")
                    # 后续注释代码略...
            except Exception as cap_err:
                 logger.error(f"Resend: Error trying to set caption on first media item: {cap_err}") 

            try:
                logger.info(f"Resending media group with {len(media_items_input_group)} items using file_ids for note {note_id_for_logs}.")
                async with TELEGRAM_SEND_LOCK:
                    await bot.send_media_group(TARGET_CHAT_ID, media_items_input_group)
            except RPCError as e:
                sent_media_successfully = False
                error_msg_user = f"错误：重新发送媒体组失败 (笔记ID: {note_id_for_logs})。\nTelegram 错误: {e}" # Ensure this is one line
                logger.error(f"Failed to resend media group using file_ids for note {note_id_for_logs}: {e}", exc_info=True)
                await safe_send_long_text(error_msg_user)
    
    sent_zip_successfully = True
    if zip_file_id_to_send:
        try:
            logger.info(f"Resending ZIP document with file_id {zip_file_id_to_send} for note {note_id_for_logs}.")
            async with TELEGRAM_SEND_LOCK:
                await bot.send_document(
                    chat_id=TARGET_CHAT_ID,
                document=zip_file_id_to_send,
                caption=full_caption_text_resend,
                reply_markup=keyboard_resend
            )
        except RPCError as e:
            sent_zip_successfully = False
            error_msg_user = f"错误：重新发送ZIP文件失败 (笔记ID: {note_id_for_logs})。\nTelegram 错误: {e}" # Ensure this is one line
            logger.error(f"Failed to resend ZIP document using file_id {zip_file_id_to_send} for note {note_id_for_logs}: {e}", exc_info=True)
            await safe_send_long_text(error_msg_user)
    
    # Final success condition: if there were any files to send, were they sent?
    # If all_file_ids was not empty, we expect at least one successful send operation.
    if not all_file_ids: # Should have been caught earlier
        return False 
        
    # If there were media items to send, media must be successful.
    # If there was a ZIP to send, ZIP must be successful.
    # If neither, it means file_ids were present but not actionable (e.g. all unknown types)
    if not media_items_input_group and not zip_file_id_to_send:
        # This means all_file_ids existed, but none were identified as photo/video for group or as the designated ZIP doc.
        logger.warning(f"Resend: No actionable media (photo/video) or designated ZIP file_id found for note {note_id_for_logs} from file_ids: {file_ids_str}")
        # Only send user error if there were file_ids to begin with but none were usable for main actions.
        await safe_send_long_text(f"警告：笔记 {note_id_for_logs} 的 file_id 记录中未找到可识别的照片/视频或ZIP文件进行重新发送。")
        return False # No primary action was possible.

    return sent_media_successfully and sent_zip_successfully

# ------------------- 处理链接的主逻辑 -------------------
async def process_links_batch(links_list, file_lines=None, from_json=False, preserved_short_link=None):
    pending_links = links_list.copy()  # 待处理链接
    results = []
    # max_continuous_fails = 3  # 允许的最大连续失败次数 - This seems to be handled by ERROR_TRACKING now
    
    for i, link_str in enumerate(links_list):
        try:
            # 单线程顺序处理，避免在API服务重启期间并发请求
            # 尝试处理单个链接
            xhs_data = await fetch_xhs_data(link_str)
            
            # 检查是否是已删除的作品
            if xhs_data and xhs_data.get("code") == 400 and xhs_data.get("msg") == "作品已删除":
                logger.info(f"作品已删除: {link_str}")
                await safe_send_long_text(f"⚠️ 作品已删除\n链接: {link_str}\n作品ID: {xhs_data.get('data', {}).get('作品ID', 'unknown')}")
                results.append((link_str, "deleted"))  # 使用特殊标记表示已删除
                if link_str in pending_links: pending_links.remove(link_str)
                continue
            
            # 检查API响应 (Basic check before Supabase)
            if not xhs_data:
                # 只记录日志，不发送给用户（因为后面会重试）
                logger.error(f"未能获取有效数据(XHS API)，接口可能异常。链接: {link_str}")
                ERROR_TRACKING["consecutive_errors"] += 1
                
                if ERROR_TRACKING["consecutive_errors"] >= ERROR_THRESHOLD:
                    handled, is_fatal = check_and_handle_api_errors("'NoneType' object has no attribute 'get' or API call failed")
                    if handled:
                        # 如果已经处理（触发了重启或确认是致命错误）
                        if is_fatal:
                            # 致命错误，通知用户并停止
                            remaining_fatal = [l for l in pending_links if l != link_str]
                            await handle_fatal_error(remaining_fatal, "XHS API服务多次返回空数据或错误，已尝试重启但仍有问题，请手动检查")
                        # 无论是否致命，都停止当前批次的处理（等待重启或退出）
                        results.append((link_str, False))
                        # 将剩余链接也标记为失败（它们将在重启后重试）
                        for remaining_link in pending_links:
                            if remaining_link != link_str:
                                results.append((remaining_link, False))
                        return results
                
                results.append((link_str, False))
                if link_str in pending_links: pending_links.remove(link_str)
                continue
            
            data_field = xhs_data.get("data", {})
            if not data_field:
                # 只记录日志，不发送给用户（因为后面会重试）
                logger.error(f"XHS API返回数据中无 'data' 字段，可能是链接无效或cookie需更新。链接: {link_str}")
                ERROR_TRACKING["consecutive_empty_data_errors"] += 1
                
                if ERROR_TRACKING["consecutive_empty_data_errors"] >= ERROR_THRESHOLD:
                    handled, is_fatal = check_and_handle_api_errors("API返回数据为空或无 'data' 字段")
                    if handled:
                        # 如果已经处理（触发了重启或确认是致命错误）
                        if is_fatal:
                            # 致命错误，通知用户并停止
                            remaining_fatal = [l for l in pending_links if l != link_str]
                            await handle_fatal_error(remaining_fatal, "XHS API多次获取到空数据或无 'data' 字段，已尝试更新cookie/重启但仍有问题，请手动检查")
                        # 无论是否致命，都停止当前批次的处理（等待重启或退出）
                        results.append((link_str, False))
                        # 将剩余链接也标记为失败（它们将在重启后重试）
                        for remaining_link in pending_links:
                            if remaining_link != link_str:
                                results.append((remaining_link, False))
                        return results
                        
                results.append((link_str, False))
                if link_str in pending_links: pending_links.remove(link_str)
                continue

            # --- Supabase Check and Resend Logic ---
            actual_note_id = data_field.get("作品ID")
            if not actual_note_id:
                logger.error(f"XHS API返回数据中缺少 作品ID。链接: {link_str}, 数据: {data_field}")
                await safe_send_long_text(f"错误：无法从API响应中获取 作品ID。链接: {link_str}")
                results.append((link_str, False))
                if link_str in pending_links: pending_links.remove(link_str)
                continue

            if supabase: # Global Supabase client check
                existing_note_data_from_db = None
                try:
                    # 不使用 maybe_single()，改用普通数组查询
                    db_response_obj = await asyncio.to_thread(
                        supabase.table("xhs_notes").select("*").eq("id", actual_note_id).execute
                    )
                    if db_response_obj and hasattr(db_response_obj, 'data'):
                        # 处理返回的数组结果，查看是否有匹配的记录
                        notes_array = db_response_obj.data
                        if notes_array and len(notes_array) > 0:
                            # 取第一个匹配的记录
                            existing_note_data_from_db = notes_array[0]
                        else:
                            # 数组为空，表示没有找到匹配的记录
                            logger.info(f"Note {actual_note_id} not found in Supabase, will process as new.")
                            existing_note_data_from_db = None
                    else:
                        # 这种情况处理 execute() 返回 None 的情况
                        logger.error(f"Supabase query for note_id {actual_note_id} returned an invalid response object. HTTP status might have been problematic.")
                        existing_note_data_from_db = None

                except Exception as e:
                    logger.error(f"Supabase query for note_id {actual_note_id} failed with an exception: {e}", exc_info=True)
                    existing_note_data_from_db = None

                if existing_note_data_from_db and existing_note_data_from_db.get("file_id"):
                    logger.info(f"Note {actual_note_id} found in Supabase with file_ids. Attempting to resend.")
                    
                    # For resend, use links from DB if available, otherwise from current context
                    work_link_for_button = existing_note_data_from_db.get("note_link") or link_str
                    author_link_for_button = existing_note_data_from_db.get("author_link") or data_field.get("作者链接") or "https://www.xiaohongshu.com/"
                    
                    resend_success = await resend_from_supabase(
                        existing_note_data_from_db,
                        work_link_for_button,
                        author_link_for_button
                    )
                    
                    results.append((link_str, resend_success))
                    if resend_success:
                        logger.info(f"Successfully resent note {actual_note_id} using file_ids from Supabase.")
                        if from_json and file_lines: # Comment out if processed from links.json
                            comment_out_link_in_file(file_lines, link_str)
                            rewrite_links_file(file_lines) # Persist change immediately
                    else:
                        logger.error(f"Failed to resend note {actual_note_id} from Supabase. This link processing is now considered failed.")
                        # resend_from_supabase is responsible for sending user-facing error messages about the resend failure itself.
                    
                    if link_str in pending_links: pending_links.remove(link_str) # Processed (or resend attempted), remove from pending
                    continue # Move to the next link in links_list, DO NOT FALLBACK to process_single_link_content
                
                elif existing_note_data_from_db and not existing_note_data_from_db.get("file_id"):
                    logger.warning(f"Note {actual_note_id} found in Supabase but has NO file_ids. Proceeding with full processing as new.")
                # else: note not found in DB, or supabase client is None. Proceed with full processing.
            
            # --- End Supabase Check --- 

            # If not continued (i.e. not resent from Supabase), proceed with normal processing
            ERROR_TRACKING["consecutive_errors"] = 0 # Reset API error counts if data was fine
            ERROR_TRACKING["consecutive_empty_data_errors"] = 0
            
            success_new_processing = await process_single_link_content(link_str, xhs_data, file_lines, from_json, preserved_short_link)
            results.append((link_str, success_new_processing))
            if success_new_processing:
                if link_str in pending_links: pending_links.remove(link_str)
            # If new processing fails, it's handled within process_single_link_content or by general exception handler
            
        except Exception as e:
            error_msg_exception = str(e)
            logger.error(f"处理链接 {link_str} 时出现未捕获的顶层异常: {error_msg_exception}", exc_info=True)
            ERROR_TRACKING["consecutive_errors"] += 1 
            
            # Check if this general exception needs service restart
            handled, is_fatal_general = check_and_handle_api_errors(error_msg_exception)
            if is_fatal_general:
                remaining_fatal_general = [l for l in pending_links if l != link_str]
                if remaining_fatal_general : # only call if there are other links that would be affected
                    await handle_fatal_error(remaining_fatal_general, f"检测到严重API异常 ({error_msg_exception}) 且无法修复，已暂停所有处理")
                results.append((link_str, False))
                return results # Stop all further processing
            else:
                # General error, not necessarily API related or not meeting restart criteria
                # 只记录日志，不发送给用户（最终统计会显示失败）
                logger.error(f"链接: {link_str} - 处理时出现意外错误: {error_msg_exception}")
                results.append((link_str, False))
                if link_str in pending_links: pending_links.remove(link_str)
    
    return results

# 将处理内容分离为独立函数，便于处理逻辑分离
async def process_single_link_content(link_str: str, xhs_data: dict, file_lines, from_json=False, preserved_short_link=None) -> bool:
    telegram_file_ids = [] # Initialize list to store file_ids
    try:
        data_field = xhs_data.get("data", {})
        if not data_field: # Added a check for empty data_field itself
            logger.error(f"XHS data block is empty for link: {link_str}")
            return False
            
        work_id = data_field.get("作品ID", "unknown")
        author_nickname = data_field.get("作者昵称", "unknown_author")
        author_nickname = re.sub(r'[\\/*?:"<>|]', "_", author_nickname)

        author_link = data_field.get("作者链接", "https://www.xiaohongshu.com/")
        work_link = link_str
        exclude_fields = {"作品链接", "作者链接", "下载地址", "动图地址"}
        info_lines = []
        for k, v in data_field.items():
            if not v or k in exclude_fields:
                continue
            info_lines.append(f"{k}: {v}")
        full_caption_text = "\n".join(info_lines)

        # 获取作者ID用于订阅按钮
        author_id_for_sub = data_field.get("作者ID", "")
        keyboard = InlineKeyboardMarkup([
            [
                InlineKeyboardButton("打开作品", url=work_link),
                InlineKeyboardButton("作者主页", url=author_link)
            ],
            [
                InlineKeyboardButton("🔄 重新处理", callback_data=f"reprocess_{work_id}"),
                InlineKeyboardButton("💾 保存JSON", callback_data=f"savejson_{work_id}")
            ],
            [
                InlineKeyboardButton("⭐ 订阅作者", callback_data=f"sub_{author_id_for_sub}")
            ]
        ])

        # 检查是否有动图地址，并且该地址列表不为空且包含有效URL
        gif_urls_list = data_field.get("动图地址", [])
        # Ensure gif_urls_list is a list and contains at least one non-empty string URL
        has_gif_urls = isinstance(gif_urls_list, list) and any(url_str for url_str in gif_urls_list if isinstance(url_str, str) and url_str.strip())
        
        # 打印API原始返回的媒体链接
        logger.info("=" * 80)
        logger.info(f"🔗 【API原始返回的媒体链接】 作品ID: {work_id}")
        logger.info("=" * 80)
        
        # 打印下载地址
        raw_download_addrs = data_field.get("下载地址", [])
        if raw_download_addrs:
            logger.info(f"📥 下载地址 (共{len(raw_download_addrs)}个):")
            for idx, url in enumerate(raw_download_addrs, 1):
                if url:
                    logger.info(f"  [{idx}] {url}")
        else:
            logger.info("📥 下载地址: 无")
        
        # 打印动图地址
        raw_gif_addrs = data_field.get("动图地址", [])
        if raw_gif_addrs:
            logger.info(f"🎞️ 动图地址 (共{len(raw_gif_addrs)}个):")
            for idx, url in enumerate(raw_gif_addrs, 1):
                if url:
                    logger.info(f"  [{idx}] {url}")
        else:
            logger.info("🎞️ 动图地址: 无")
        
        logger.info("=" * 80)
        
        # 统一使用本地API返回的链接（本地API已支持动图和视频）
        download_addrs = data_field.get("下载地址", []) or []
        gif_addrs = data_field.get("动图地址", []) or []
        all_media_urls = [u for u in download_addrs if u] + [u for u in gif_addrs if u]
        
        if gif_addrs and any(gif_addrs):
            logger.info(f"检测到动图地址，作品ID: {work_id}")
            logger.info(f"动图链接: {[u for u in gif_addrs if u]}")

        if not all_media_urls:
            await safe_send_long_text(f"链接: {link_str}\n没有可下载的媒体文件。")
            return False

        video_files = []
        image_files = []
        jpeg_files = []  # 用于存储JPEG版本的图片路径（用于Telegram发送）
        
        logger.info(f"Processing {len(all_media_urls)} media URLs for note {work_id}: {all_media_urls}") # LOGGING ADDED
        
        # 判断作品类型
        work_type = data_field.get("作品类型", "")
        is_video_work = work_type == "视频"
        logger.info(f"作品类型: {work_type}, 视频作品: {is_video_work}")
        
        # 并发下载所有媒体资源
        logger.info(f"开始并发下载 {len(all_media_urls)} 个媒体文件...")
        download_tasks = []
        for idx, media_url in enumerate(all_media_urls):
            # 创建下载任务
            task = download_original(media_url, BASE_DOWNLOAD_FOLDER, work_id, author_nickname, idx+1, is_video_work)
            download_tasks.append(task)
        
        # 等待所有下载任务完成
        download_results = await asyncio.gather(*download_tasks, return_exceptions=True)
        
        # 处理下载结果
        for idx, result in enumerate(download_results):
            media_url = all_media_urls[idx]
            
            # 处理异常情况
            if isinstance(result, Exception):
                logger.error(f"下载失败 {media_url}: {result}")
                continue
                
            if len(result) == 3:
                fp, file_type, jpeg_fp = result
                
                # 修改逻辑：即使PNG为空，但如果JPEG成功则继续处理
                if file_type == "video" and not fp:
                    # 视频文件必须成功下载
                    continue
                elif file_type == "image":
                    # 图片情况：即使PNG失败，只要JPEG成功，就使用JPEG
                    if not fp and not jpeg_fp:
                        # 两种格式都失败才跳过
                        continue
                    
                    # 如果只有JPEG成功，用JPEG路径填充image_files
                    if not fp and jpeg_fp:
                        image_files.append(jpeg_fp)  # 将JPEG文件也加入PNG列表，用于ZIP打包
                        jpeg_files.append(jpeg_fp)
                    else:
                        # 正常情况：PNG成功
                        image_files.append(fp)
                        if jpeg_fp:
                            jpeg_files.append(jpeg_fp)
                elif file_type == "video" or is_valid_video(fp):
                    video_files.append(fp)
            else:
                # 兼容旧版本下载函数的返回格式（无JPEG）
                fp = result[0] if isinstance(result, tuple) else result
                logger.warning(f"Download original returned unexpected result format for {media_url}. Result: {result}") # LOGGING ADDED
                if not fp:
                    continue
                if is_valid_video(fp):
                    video_files.append(fp)
                else:
                    image_files.append(fp)
        
        logger.info(f"并发下载完成，成功下载 {len(image_files)} 个图片，{len(video_files)} 个视频")

        logger.info(f"Post-download stats for note {work_id}:") # LOGGING ADDED
        logger.info(f"  PNG/JPEG Image files (for ZIP): {[(f, os.path.getsize(f) if os.path.exists(f) else 0) for f in image_files]}") # 修改描述
        logger.info(f"  JPEG Image files (for Telegram): {[(f, os.path.getsize(f) if os.path.exists(f) else 0) for f in jpeg_files]}") # LOGGING ADDED
        logger.info(f"  Video files: {[(f, os.path.getsize(f) if os.path.exists(f) else 0) for f in video_files]}") # LOGGING ADDED

        if not video_files and not image_files: 
            # 只记录日志，不发送给用户（最终统计会显示失败）
            logger.error(f"链接: {link_str} - 所有媒体下载失败")
            return False

        # 单线程处理，移除UPLOAD_SEM
        # 准备所有媒体文件，优先使用JPEG格式的图片
        all_media_files = []
        silent_videos = []  # 存储无音轨视频，这些视频不会尝试单独发送，只放入ZIP
        
        # 添加图片文件（优先使用JPEG版本）
        for fp in (jpeg_files if jpeg_files else image_files):
            if os.path.getsize(fp) <= MAX_IMAGE_SEND_SIZE:  # 跳过超大图片
                all_media_files.append({"file": fp, "type": "photo"})
            
        # 添加视频文件，但先检查是否有音轨
        for fp in video_files:
            if os.path.getsize(fp) <= MAX_VIDEO_SEND_SIZE:  # 跳过超大视频
                # 检查视频是否有音轨
                _, _, _, has_audio = get_video_info(fp)
                if has_audio:
                    all_media_files.append({"file": fp, "type": "video"})
                else:
                    logger.warning(f"视频 {fp} 没有音轨，将只添加到ZIP包中")
                    silent_videos.append(fp)
        
        # 创建媒体组 (最多取前10个)
        media_group = []
        short_cap = safe_truncate_caption(full_caption_text, MAX_CAPTION_LEN)
        
        # 最多只取前10个文件（Telegram媒体组限制）
        files_to_send = all_media_files[:MAX_MEDIA_GROUP_COUNT]
        
        # 为了便于调试，记录文件信息
        logger.info(f"准备在媒体组中发送的文件数量: {len(files_to_send)}")
        logger.info(f"无音轨视频数量（仅添加到ZIP）: {len(silent_videos)}")
        for idx, item in enumerate(files_to_send):
            logger.info(f"文件 {idx+1}: {item['file']} (类型: {item['type']})")
        
        # 构建媒体组对象
        for item in files_to_send:
            if item["type"] == "photo":
                media_obj = InputMediaPhoto(item["file"])
            else:  # video
                media_obj = InputMediaVideo(item["file"])
            
            # 移除媒体组的说明文字，ZIP文件已包含完整信息
            # if len(media_group) == 0:
            #     media_obj.caption = short_cap
            
            media_group.append(media_obj)
        
        # 发送媒体组
        image_files_failed = []  # 记录发送失败的图片文件
        video_files_failed = []  # 记录发送失败的视频文件
        
        if media_group:
            try:
                logger.info(f"尝试发送媒体组 ({len(media_group)} 个文件)")
                async with TELEGRAM_SEND_LOCK:
                    sent_messages = await bot.send_media_group(TARGET_CHAT_ID, media_group)
                
                if sent_messages:
                    logger.info(f"媒体组发送成功，获取文件ID")
                    for msg in sent_messages:
                        file_id = extract_file_id_from_message(msg)
                        if file_id:
                            telegram_file_ids.append(file_id)
                            logger.info(f"获取到file_id: {file_id}")
            except RPCError as e:
                if "MEDIA_EMPTY" in str(e):
                    logger.error(f"媒体组发送失败，可能包含无音轨视频: {e}")
                    # 分离图片和视频文件，只尝试单独发送图片
                    for item in files_to_send:
                        if item["type"] == "photo":
                            image_files_failed.append(item["file"])
                        elif item["type"] == "video":
                            # 视频可能导致问题，放入失败视频列表但不尝试单独发送
                            video_files_failed.append(item["file"])
                            logger.warning(f"不会尝试单独发送可能导致错误的视频: {item['file']}")
                else:
                    logger.error(f"媒体组发送失败: {e}，将尝试单独发送图片")
                    # 所有文件都尝试单独发送
                    for item in files_to_send:
                        if item["type"] == "photo":
                            image_files_failed.append(item["file"])
                        elif item["type"] == "video":
                            # 针对其他错误，视频也尝试单独发送
                            video_files_failed.append(item["file"])
        
        # 处理发送失败的图片（单独发送）
        for file_path in image_files_failed:
            try:
                logger.info(f"尝试单独发送图片: {file_path}")
                async with TELEGRAM_SEND_LOCK:
                    sent_photo_msg = await bot.send_photo(
                    chat_id=TARGET_CHAT_ID,
                photo=file_path,
                caption=short_cap
                )
                file_id = extract_file_id_from_message(sent_photo_msg)
                if file_id:
                    telegram_file_ids.append(file_id)
                    logger.info(f"图片单独发送成功，file_id: {file_id}")
            except RPCError as e:
                # 如果图片发送失败，尝试作为文档发送
                logger.warning(f"图片发送失败 ({e})，尝试作为文档发送")
                try:
                    async with TELEGRAM_SEND_LOCK:
                        sent_doc_msg = await bot.send_document(
                            chat_id=TARGET_CHAT_ID,
                        document=file_path,
                        caption=short_cap
                    )
                    file_id = extract_file_id_from_message(sent_doc_msg)
                    if file_id:
                        telegram_file_ids.append(file_id)
                        logger.info(f"图片作为文档发送成功，file_id: {file_id}")
                except RPCError as e2:
                    logger.error(f"图片作为文档也发送失败: {e2}")
        
        # 处理需要单独发送的视频（只有在非MEDIA_EMPTY错误时才尝试）
        for file_path in video_files_failed:
            # 再次检查视频是否有音轨
            _, _, _, has_audio = get_video_info(file_path)
            if not has_audio:
                logger.info(f"跳过发送无音轨视频 {file_path}，将只包含在ZIP中")
                # 确保无音轨视频添加到silent_videos列表中
                if file_path not in silent_videos:
                    silent_videos.append(file_path)
                continue
            
            try:
                # 尝试作为视频发送
                logger.info(f"尝试单独发送有音轨视频: {file_path}")
                async with TELEGRAM_SEND_LOCK:
                    sent_video_msg = await bot.send_video(
                        chat_id=TARGET_CHAT_ID,
                    video=file_path,
                    caption=short_cap
                )
                file_id = extract_file_id_from_message(sent_video_msg)
                if file_id:
                    telegram_file_ids.append(file_id)
                    logger.info(f"视频单独发送成功，file_id: {file_id}")
            except RPCError as e:
                # 如果视频发送失败，尝试作为文档发送
                logger.warning(f"视频发送失败 ({e})，尝试作为文档发送")
                try:
                    async with TELEGRAM_SEND_LOCK:
                        sent_doc_msg = await bot.send_document(
                            chat_id=TARGET_CHAT_ID,
                        document=file_path,
                        caption=short_cap
                    )
                    file_id = extract_file_id_from_message(sent_doc_msg)
                    if file_id:
                        telegram_file_ids.append(file_id)
                        logger.info(f"视频作为文档发送成功，file_id: {file_id}")
                except RPCError as e2:
                    logger.error(f"视频作为文档也发送失败: {e2}")
        
        # 打包发送 ZIP（包含所有文件，无论是否已发送，确保静音视频也被包含）
        all_files_for_zip = video_files + image_files  # 只用PNG版本打包ZIP，不用WebP版本
        zip_path = make_zip_of_media(all_files_for_zip, work_id)
        if zip_path and os.path.exists(zip_path):
            try:
                logger.info(f"尝试发送ZIP包: {zip_path}")
                async with TELEGRAM_SEND_LOCK:
                    sent_doc_msg = await bot.send_document(
                        chat_id=TARGET_CHAT_ID,
                    document=zip_path,
                    caption=full_caption_text,
                    reply_markup=keyboard
                )
                file_id = extract_file_id_from_message(sent_doc_msg)
                if file_id:
                    telegram_file_ids.append(file_id)
                    logger.info(f"ZIP包发送成功，file_id: {file_id}")
                
                # 清理临时文件：删除ZIP、JPEG图片和视频缩略图，保留PNG图片
                cleanup_temp_files([zip_path], video_files, jpeg_files)
            except RPCError as e:
                # 只记录日志，不发送给用户（最终统计会显示失败）
                logger.error(f"发送ZIP包失败: {e} - 链接: {link_str}")
        else:
            # 只记录日志，不发送给用户（最终统计会显示失败）
            logger.error(f"ZIP包创建失败 - 链接: {link_str}")

        # 注释掉已处理成功的链接
        if from_json and file_lines:
            comment_out_link_in_file(file_lines, link_str)
            rewrite_links_file(file_lines)

        # --- Supabase Integration Call ---
        if supabase: # Check if supabase client is available and initialized
            # Upsert user info first
            user_upsert_success = await upsert_xhs_user_to_supabase(data_field)
            if user_upsert_success:
                logger.info(f"Supabase: Successfully upserted user data for author ID: {data_field.get('作者ID')}")
            else:
                logger.warning(f"Supabase: Failed to upsert user data for author ID: {data_field.get('作者ID')}")

            # Then upsert note info
            # 确定要保存的短链接
            short_link = None
            if preserved_short_link:
                # 如果有保留的短链接（重新处理场景），使用保留的
                short_link = preserved_short_link
                logger.info(f"使用保留的短链接: {short_link}")
            elif "xhslink.com" in link_str.lower():
                # 否则，如果当前链接是短链接，保存当前链接
                short_link = link_str
                logger.info(f"检测到新的短链接: {short_link}")
            
            file_ids_str = ";".join(filter(None, telegram_file_ids)) if telegram_file_ids else None
            note_upsert_success = await upsert_xhs_note_to_supabase(data_field, file_ids_str, short_link, link_str)
            if note_upsert_success:
                logger.info(f"Supabase: Successfully upserted note data for note ID: {work_id} with file_ids: {file_ids_str if file_ids_str else 'None'}, short_link: {short_link}")
            else:
                logger.warning(f"Supabase: Failed to upsert note data for note ID: {work_id}")
        # --- End Supabase Integration Call ---

        return True
    except Exception as e:
        error_msg = str(e)
        logger.error(f"处理链接内容时出现异常: {error_msg}", exc_info=True)
        await safe_send_long_text(f"链接: {link_str}\n处理内容时出现异常: {error_msg}")
        return False

# ------------------- 临时文件清理 -------------------
def cleanup_temp_files(zip_paths, video_files, jpeg_files=None):
    """
    清理临时文件：
    - 删除ZIP文件
    - 删除视频的缩略图
    - 删除JPEG格式的图片（如果有）
    
    PNG格式的图片会保留
    
    Args:
        zip_paths: ZIP文件路径列表
        video_files: 视频文件路径列表
        jpeg_files: JPEG图片路径列表
    """
    # 删除ZIP文件
    for zp in zip_paths:
        if zp and os.path.exists(zp):
            try:
                os.remove(zp)
                logger.debug(f"已删除ZIP文件: {zp}")
            except Exception as e:
                logger.error(f"删除ZIP文件失败: {zp}, 错误: {e}")
    
    # 删除视频缩略图
    for vf in video_files:
        thumb_fp = vf + "_thumb.jpg"
        if os.path.exists(thumb_fp):
            try:
                os.remove(thumb_fp)
                logger.debug(f"已删除视频缩略图: {thumb_fp}")
            except Exception as e:
                logger.error(f"删除视频缩略图失败: {thumb_fp}, 错误: {e}")
    
    # 删除JPEG图片
    if jpeg_files:
        for jf in jpeg_files:
            if jf and os.path.exists(jf):
                try:
                    os.remove(jf)
                    logger.debug(f"已删除JPEG图片: {jf}")
                except Exception as e:
                    logger.error(f"删除JPEG图片失败: {jf}, 错误: {e}")

async def check_note_deleted_via_tikhub(note_id: str) -> dict:
    """
    使用TikHub API检查作品状态并获取媒体信息
    
    Args:
        note_id: 小红书作品ID
        
    Returns:
        dict: 包含状态和媒体信息的字典
            {
                'deleted': bool,  # 是否已删除
                'exists': bool,   # 是否存在
                'type': str,      # 作品类型: 'image', 'video', 'gif'
                'media_urls': list,  # 最高画质的媒体URL列表
                'error': str      # 错误信息（如果有）
            }
    """
    result = {
        'deleted': False,
        'exists': False,
        'type': None,
        'media_urls': [],
        'error': None
    }
    
    if not XHS_TIKHUB_API_TOKEN:
        logger.warning("TikHub API Token未配置，无法检查作品状态")
        result['error'] = "No API token"
        return result
    
    try:
        # TikHub 小红书 API URL (正确的端点)
        api_url = f"https://api.tikhub.io/api/v1/xiaohongshu/app/get_note_info_v2?note_id={note_id}"
        headers = {
            'accept': 'application/json',
            'Authorization': f'Bearer {XHS_TIKHUB_API_TOKEN}'
        }
        
        logger.info(f"通过TikHub API检查作品状态: {note_id}")
        response = await asyncio.to_thread(requests.get, api_url, headers=headers, timeout=30)
        
        # 检查响应
        if response.status_code == 400:
            data = response.json()
            # 检查是否是作品已删除的特定错误
            if "detail" in data and data["detail"].get("code") == 400:
                logger.info(f"TikHub API确认作品已删除: {note_id}")
                result['deleted'] = True
                return result
        elif response.status_code == 200:
            data = response.json()
            
            # 检查API返回的状态码
            if data.get('code') != 200:
                result['error'] = f"API returned code {data.get('code')}"
                return result
            
            # 获取作品数据
            note_data = data.get('data', {}).get('data', {})
            if not note_data:
                result['error'] = "No note data in response"
                return result
            
            # 作品存在
            result['exists'] = True
            
            # 判断作品类型并获取媒体URL
            note_type = note_data.get('type')
            video_info = note_data.get('videoInfo')
            images_list = note_data.get('imagesList', [])
            
            # type=2 表示视频作品
            if note_type == 2 or (video_info and (video_info.get('videoUrl') or video_info.get('url'))):
                # 视频作品
                result['type'] = 'video'
                
                # 获取视频URL - 优先使用videoUrl字段
                video_url = video_info.get('videoUrl') or video_info.get('url')
                if video_url:
                    result['media_urls'].append(video_url)
                
                # 注释掉originVideoKey，因为它通常无法直接访问
                # if video_info.get('originVideoKey'):
                #     origin_url = f"http://ci.xiaohongshu.com/{video_info['originVideoKey']}"
                #     if origin_url not in result['media_urls']:
                #         result['media_urls'].append(origin_url)
                
                # 添加封面图（视频作品通常在imagesList中有封面）
                if images_list:
                    for img in images_list:
                        cover_url = img.get('original') or img.get('url')
                        if cover_url and cover_url not in result['media_urls']:
                            result['media_urls'].append(cover_url)
                            
            elif note_type == 1 or images_list:
                # 图片/动图作品 - type=1 或有imagesList但无videoInfo
                result['type'] = 'image'  # 默认为图片
                
                for img in images_list:
                    # 使用original字段获取最高画质
                    original_url = img.get('original')
                    if original_url:
                        result['media_urls'].append(original_url)
                        # 检查是否为动图（通过URL或其他标识判断）
                        # spectrum.netease.com 是网易的动图CDN
                        if 'spectrum' in original_url or '.gif' in original_url.lower():
                            result['type'] = 'gif'
                    elif img.get('url'):
                        # 如果没有original，使用普通URL
                        result['media_urls'].append(img['url'])
                        if 'spectrum' in img['url'] or '.gif' in img['url'].lower():
                            result['type'] = 'gif'
            
            logger.info(f"TikHub API获取作品信息成功: {note_id}, 类型: {result['type']}, 媒体数: {len(result['media_urls'])}")
            return result
            
        else:
            logger.warning(f"TikHub API返回未知状态码 {response.status_code}: {note_id}")
            result['error'] = f"HTTP {response.status_code}"
            return result
            
    except Exception as e:
        logger.error(f"TikHub API请求失败: {e}")
        result['error'] = str(e)
        return result

async def download_tikhub_media_file(url: str, folder_path: str, work_id: str, author_nickname: str, index: int, is_video: bool) -> tuple[str, str, str]:
    """
    下载TikHub媒体文件（与download_original类似）
    
    Returns:
        tuple[str, str, str]: (filepath, file_type, jpeg_filepath)
    """
    try:
        if is_video:
            # 下载视频
            logger.info(f"📥 开始下载TikHub视频 #{index}")
            content, content_type = download_video_multithread(url)
            if content:
                safe_nickname = re.sub(r'[\\/*?:"<>|]', "_", author_nickname)[:20]
                filename = f"xhs_{work_id}_{safe_nickname}_{index}.mp4"
                filepath = os.path.join(folder_path, filename)
                with open(filepath, 'wb') as f:
                    f.write(content)
                logger.info(f"TikHub视频下载成功: {filepath} (大小: {len(content)} bytes)")
                return filepath, 'video', ''
        else:
            # 下载图片 - 确保获取最高画质
            logger.info(f"📥 开始下载TikHub图片 #{index}")
            
            # 去掉URL参数获取原图
            original_url = url.split('?')[0] if '?' in url else url
            
            # 下载原图
            content, content_type = download_file(original_url)
            if content:
                # 判断格式
                if 'image/png' in content_type or content.startswith(b'\x89PNG\r\n\x1a\n'):
                    ext = 'png'
                elif 'image/webp' in content_type or (content.startswith(b'RIFF') and content[8:12] == b'WEBP'):
                    ext = 'webp'
                elif 'image/gif' in content_type or content.startswith(b'GIF87a') or content.startswith(b'GIF89a'):
                    ext = 'gif'
                else:
                    ext = 'jpg'
                
                safe_nickname = re.sub(r'[\\/*?:"<>|]', "_", author_nickname)[:20]
                filename = f"xhs_{work_id}_{safe_nickname}_{index}.{ext}"
                filepath = os.path.join(folder_path, filename)
                with open(filepath, 'wb') as f:
                    f.write(content)
                
                # 如果不是JPEG，创建JPEG版本用于Telegram发送
                jpeg_filepath = ''
                if ext != 'jpg':
                    try:
                        from PIL import Image
                        img = Image.open(filepath)
                        if img.mode in ('RGBA', 'LA'):
                            rgb_img = Image.new('RGB', img.size, (255, 255, 255))
                            rgb_img.paste(img, mask=img.split()[-1] if img.mode == 'RGBA' else None)
                            img = rgb_img
                        jpeg_filename = f"xhs_{work_id}_{safe_nickname}_{index}_tg.jpg"
                        jpeg_filepath = os.path.join(folder_path, jpeg_filename)
                        img.save(jpeg_filepath, 'JPEG', quality=85)
                        logger.info(f"创建JPEG版本: {jpeg_filepath}")
                    except Exception as e:
                        logger.warning(f"创建JPEG版本失败: {e}")
                        jpeg_filepath = filepath  # 使用原文件
                else:
                    jpeg_filepath = filepath
                
                logger.info(f"TikHub图片下载成功: {filepath} (格式: {ext})")
                return filepath, 'image', jpeg_filepath
                
    except Exception as e:
        logger.error(f"下载TikHub媒体失败: {e}")
        return '', '', ''
    
    return '', '', ''

async def process_tikhub_media(link_str: str, tikhub_xhs_data: dict, tikhub_result: dict) -> bool:
    """
    使用TikHub API获取的媒体资源进行下载和发送
    与本地API处理方式保持一致
    
    Args:
        link_str: 原始链接
        tikhub_xhs_data: 构造的xhs_data格式数据
        tikhub_result: TikHub API返回的结果
        
    Returns:
        bool: 处理是否成功
    """
    try:
        work_id = tikhub_xhs_data["data"]["作品ID"]
        media_urls = tikhub_result['media_urls']
        media_type = tikhub_result['type']
        
        logger.info(f"开始处理TikHub媒体: {work_id}, 类型: {media_type}, 媒体数: {len(media_urls)}")
        
        # 使用统一的下载文件夹（与本地API一致，不创建子文件夹）
        BASE_DOWNLOAD_FOLDER = "download"
        
        # 准备下载任务
        is_video_work = media_type == 'video'
        author_nickname = "TikHub_User"  # TikHub没有作者信息
        
        # 并发下载所有媒体
        download_tasks = []
        for idx, media_url in enumerate(media_urls):
            # 对于视频作品，第一个是视频，其他是封面
            is_current_video = is_video_work and idx == 0
            task = download_tikhub_media_file(media_url, BASE_DOWNLOAD_FOLDER, work_id, author_nickname, idx+1, is_current_video)
            download_tasks.append(task)
        
        download_results = await asyncio.gather(*download_tasks, return_exceptions=True)
        
        # 收集成功下载的文件
        video_files = []
        image_files = []
        jpeg_files = []  # 用于Telegram发送
        
        for idx, result in enumerate(download_results):
            if isinstance(result, Exception):
                logger.error(f"下载TikHub媒体失败 [{idx+1}]: {result}")
                continue
            
            file_path, file_type, jpeg_path = result
            if file_path and os.path.exists(file_path):
                if file_type == 'video':
                    video_files.append(file_path)
                else:
                    image_files.append(file_path)
                    if jpeg_path and os.path.exists(jpeg_path):
                        jpeg_files.append(jpeg_path)
                
        if not video_files and not image_files:
            logger.error(f"TikHub: 所有媒体下载失败 - {link_str}")
            return False
        
        # 准备发送到Telegram
        # 构建caption
        caption_lines = [
            f"📦 TikHub备用下载",
            f"🔗 作品ID: {work_id}",
            f"📊 类型: {'视频' if is_video_work else '图片'}作品",
            f"📁 包含: {len(image_files)}张图片, {len(video_files)}个视频"
        ]
        full_caption_text = "\n".join(caption_lines)
                    
        # 准备媒体组（与本地API一致）
        all_media_files = []
        
        # 添加图片文件（优先使用JPEG版本）
        for fp in (jpeg_files if jpeg_files else image_files):
            if os.path.getsize(fp) <= MAX_IMAGE_SEND_SIZE:
                all_media_files.append({"file": fp, "type": "photo"})
        
        # 添加视频文件
        for fp in video_files:
            if os.path.getsize(fp) <= MAX_VIDEO_SEND_SIZE:
                # 检查视频是否有音轨
                _, _, _, has_audio = get_video_info(fp)
                if has_audio:
                    all_media_files.append({"file": fp, "type": "video"})
        
        # 创建媒体组发送（与本地API一致）
        media_group = []
        files_to_send = all_media_files[:MAX_MEDIA_GROUP_COUNT]  # 最多10个
        
        if files_to_send:
            logger.info(f"准备发送TikHub媒体组: {len(files_to_send)}个文件")
            for item in files_to_send:
                if item["type"] == "photo":
                    media_obj = InputMediaPhoto(item["file"])
                else:  # video
                    media_obj = InputMediaVideo(item["file"])
                media_group.append(media_obj)
            
            # 发送媒体组
            try:
                async with TELEGRAM_SEND_LOCK:
                    sent_messages = await bot.send_media_group(
                        chat_id=TARGET_CHAT_ID,
                    media=media_group
                )
                logger.info(f"TikHub媒体组发送成功: {len(sent_messages)}个文件")
            except Exception as e:
                logger.warning(f"TikHub媒体组发送失败: {e}")
        
        # 创建ZIP包（包含所有文件）
        zip_filename = f"xhs_{work_id}_tikhub.zip"
        zip_path = os.path.join(BASE_DOWNLOAD_FOLDER, zip_filename)
        
        try:
            with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                # 添加所有原始文件（不是JPEG版本）
                for fp in image_files + video_files:
                    if os.path.exists(fp):
                        arcname = os.path.basename(fp)
                        zipf.write(fp, arcname)
                        logger.debug(f"添加到ZIP: {arcname}")
        except Exception as e:
            logger.error(f"创建ZIP包失败: {e}")
            zip_path = None
        
        # 发送ZIP包
        if zip_path and os.path.exists(zip_path):
            try:
                caption = f"📦 TikHub备用下载\n"
                caption += f"🔗 作品ID: {work_id}\n"
                caption += f"📊 包含: {len(image_files)}张图片, {len(video_files)}个视频"
                
                async with TELEGRAM_SEND_LOCK:
                    await bot.send_document(
                        chat_id=TARGET_CHAT_ID,
                    document=zip_path,
                    caption=caption,
                    file_name=zip_filename
                )
                logger.info(f"TikHub ZIP包发送成功: {zip_filename}")
            except Exception as e:
                logger.error(f"发送TikHub ZIP包失败: {e}")
        
        # 清理临时文件
        try:
            # 删除JPEG版本
            for fp in jpeg_files:
                if fp not in image_files and os.path.exists(fp):
                    os.remove(fp)
                    logger.debug(f"删除JPEG临时文件: {fp}")
            # 删除ZIP
            if zip_path and os.path.exists(zip_path):
                os.remove(zip_path)
        except Exception as e:
            logger.warning(f"清理TikHub临时文件失败: {e}")
        
        # 保存到数据库（与本地API一致）
        try:
            # 构建file_ids_str（如果有telegram消息ID）
            file_ids_str = None
            
            # 保存到数据库
            note_upsert_success = await upsert_xhs_note_to_supabase(
                tikhub_xhs_data, 
                file_ids_str,
                link_str if link_str.startswith("http://xhslink.com/") else None,  # 短链接
                link_str  # 原始链接
            )
            
            if note_upsert_success:
                logger.info(f"TikHub Supabase: 成功保存笔记数据 ID: {work_id}")
            else:
                logger.warning(f"TikHub Supabase: 保存笔记数据失败 ID: {work_id}")
        except Exception as e:
            logger.error(f"TikHub保存到数据库失败: {e}")
        
        return True
            
    except Exception as e:
        logger.error(f"处理TikHub媒体时出错: {e}")
        return False

def download_file(url: str) -> tuple[bytes, str]:
    """
    下载文件内容
    
    Args:
        url: 文件URL
        
    Returns:
        tuple[bytes, str]: (文件内容的二进制数据, content-type)
    """
    try:
        r = requests.get(url, timeout=60)
        r.raise_for_status()
        content_type = r.headers.get('Content-Type', '').lower()
        return r.content, content_type
    except Exception as e:
        logger.error(f"下载文件 {url} 失败: {e}")
        return b"", ""

def download_video_multithread(url: str, num_threads: int = 4) -> tuple[bytes, str]:
    """
    使用多线程下载视频文件
    
    Args:
        url: 视频URL
        num_threads: 线程数量（默认4）
        
    Returns:
        tuple[bytes, str]: (文件内容的二进制数据, content-type)
    """
    try:
        # 先获取文件大小和是否支持分段下载
        head_response = requests.head(url, timeout=10, allow_redirects=True)
        content_length = head_response.headers.get('Content-Length')
        accept_ranges = head_response.headers.get('Accept-Ranges')
        content_type = head_response.headers.get('Content-Type', '').lower()
        
        # 如果不支持分段下载或文件大小未知，回退到单线程
        if not content_length or accept_ranges != 'bytes':
            logger.info(f"视频不支持分段下载，使用单线程下载: {url}")
            return download_file(url)
        
        file_size = int(content_length)
        logger.info(f"开始多线程下载视频，文件大小: {file_size} bytes, 线程数: {num_threads}")
        
        # 如果文件太小（小于1MB），直接单线程下载
        if file_size < 1024 * 1024:
            logger.info(f"文件太小 ({file_size} bytes)，使用单线程下载")
            return download_file(url)
        
        # 计算每个线程的下载范围
        chunk_size = file_size // num_threads
        ranges = []
        for i in range(num_threads):
            start = i * chunk_size
            end = file_size - 1 if i == num_threads - 1 else (i + 1) * chunk_size - 1
            ranges.append((start, end))
        
        # 存储每个线程的下载结果
        results = [None] * num_threads
        
        def download_chunk(index, start, end):
            """下载指定范围的数据块"""
            headers = {'Range': f'bytes={start}-{end}'}
            try:
                response = requests.get(url, headers=headers, timeout=60)
                response.raise_for_status()
                results[index] = response.content
                logger.debug(f"线程 {index} 成功下载 bytes {start}-{end}")
            except Exception as e:
                logger.error(f"线程 {index} 下载失败 (bytes {start}-{end}): {e}")
                results[index] = None
        
        # 创建并启动线程
        threads = []
        for i, (start, end) in enumerate(ranges):
            thread = threading.Thread(target=download_chunk, args=(i, start, end))
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join(timeout=120)  # 每个线程最多等待2分钟
        
        # 检查是否所有块都下载成功
        if any(chunk is None for chunk in results):
            logger.warning(f"多线程下载失败，部分块未能下载，回退到单线程下载")
            return download_file(url)
        
        # 合并所有块
        complete_data = b''.join(results)
        logger.info(f"多线程下载成功，总大小: {len(complete_data)} bytes")
        
        return complete_data, content_type
        
    except Exception as e:
        logger.error(f"多线程下载视频失败: {e}，回退到单线程下载")
        return download_file(url)

def guess_file_type(file_data: bytes, url: str) -> tuple[str, str]:
    """
    根据文件内容和URL猜测文件类型
    
    Args:
        file_data: 文件二进制内容
        url: 文件URL
        
    Returns:
        tuple[str, str]: (文件扩展名, 文件类型)
    """
    lower_url = url.lower()
    if any(x in lower_url for x in [".mp4", ".mov"]):
        return ("mp4", "video")
    elif ".gif" in lower_url:
        return ("gif", "image")
    elif any(x in lower_url for x in [".jpg", ".jpeg", ".png", ".webp"]):
        if file_data.startswith(b'\xff\xd8\xff'):
            return ("jpg", "image")
        elif file_data.startswith(b'\x89PNG\r\n\x1a\n'):
            return ("png", "image")
        elif file_data.startswith(b'GIF87a') or file_data.startswith(b'GIF89a'):
            return ("gif", "image")
        else:
            return ("jpg", "image")
    else:
        # 根据魔数
        if file_data.startswith(b'\xff\xd8\xff'):
            return ("jpg", "image")
        elif file_data.startswith(b'\x89PNG\r\n\x1a\n'):
            return ("png", "image")
        elif file_data.startswith(b'GIF87a') or file_data.startswith(b'GIF89a'):
            return ("gif", "image")
        elif file_data.startswith(b'\x00\x00\x00') or file_data.startswith(b'ftyp'):
            return ("mp4", "video")
        else:
            return ("bin", "unknown")

# ------------------- 一些辅助函数 -------------------
def safe_truncate_caption(text: str, max_len: int) -> str:
    if len(text) <= max_len:
        return text
    return text[:(max_len - 3)] + "..."

async def safe_send_long_text(text: str):
    chunk_size = MAX_TEXT_LEN
    start = 0
    while start < len(text):
        chunk = text[start:start + chunk_size]
        start += chunk_size
        try:
            async with TELEGRAM_SEND_LOCK:
                await bot.send_message(TARGET_CHAT_ID, chunk)
        except RPCError as e:
            logger.error(f"safe_send_long_text失败: {e}")

async def ephemeral_send_text(text: str, delay: int = 30):
    try:
        async with TELEGRAM_SEND_LOCK:
            msg = await bot.send_message(TARGET_CHAT_ID, text)
        asyncio.create_task(delete_message_after(msg.chat.id, msg.id, delay))
    except RPCError as e:
        logger.error(f"ephemeral_send_text 发送失败: {e}")

async def delete_message_after(chat_id: int, msg_id: int, delay: int):
    await asyncio.sleep(delay)
    try:
        async with TELEGRAM_SEND_LOCK:
            await bot.delete_messages(chat_id=chat_id, message_ids=[msg_id])
    except:
        pass

async def fetch_xhs_data(link: str, timeout: int = 15) -> dict:
    """
    获取小红书数据
    Args:
        link: 小红书链接
        timeout: 超时时间，默认15秒（保存JSON时可以用更短的超时）
    """
    payload = {"url": link, "download": False, "skip": False}
    try:
        logger.debug(f"开始请求API: {XHS_API_URL}, payload: {payload}")
        start = time.time()
        # 用 to_thread 包装，同步请求放到线程池
        resp = await asyncio.to_thread(requests.post, XHS_API_URL, json=payload, timeout=timeout)
        elapsed = time.time() - start
        logger.debug(f"API请求完成，耗时: {elapsed:.2f}秒, 状态码: {resp.status_code}")
        
        # 特殊处理400错误 - 可能是作品已删除
        if resp.status_code == 400:
            logger.warning(f"本地API返回400错误，可能作品已删除: {link}")
            
            # 尝试从链接中提取作品ID
            note_id = None
            # 从链接中提取作品ID的几种模式
            import re
            patterns = [
                r'/explore/([a-f0-9]{24})',  # 标准链接格式
                r'/discovery/item/([a-f0-9]{24})',  # 发现页链接
                r'note_id=([a-f0-9]{24})',  # 参数格式
                r'([a-f0-9]{24})',  # 直接匹配24位hex字符串
            ]
            
            for pattern in patterns:
                match = re.search(pattern, link)
                if match:
                    note_id = match.group(1)
                    break
            
            # 不再在这里立即调用TikHub API，而是在重试后再调用
            # 直接返回400错误，让主流程处理
            return {"code": 400, "msg": "API返回400错误，可能作品已删除或链接无效"}
        
        resp.raise_for_status()
        data = resp.json()
        # 对于保存JSON的请求，即使没有data字段也要返回完整响应
        if not data:
            logger.warning(f"API返回空数据")
            return {"code": -1, "msg": "API返回空数据"}
        # 检查是否有错误码
        if data.get("code") != 0:
            logger.warning(f"API返回错误: code={data.get('code')}, msg={data.get('msg', 'Unknown error')}")
        elif "data" not in data:
            logger.warning(f"API返回结构异常，缺少data字段: {data}")
            check_and_handle_api_errors("'NoneType' object has no attribute 'get'")
        return data  # 总是返回完整的响应，让调用方决定如何处理
    except requests.Timeout:
        logger.error(f"API请求超时 ({timeout}秒): {link}")
        return {"code": -1, "msg": f"请求超时({timeout}秒)"}
    except Exception as e:
        logger.error(f"调用后端接口异常: {e}")
        if "ConnectionError" in str(e) or "ConnectTimeout" in str(e):
            check_and_handle_api_errors("'NoneType' object has no attribute 'get'")
        return {}

def read_links_json():
    file_path = "links.json"
    if not os.path.isfile(file_path):
        return None, []
    with open(file_path, "r", encoding="utf-8") as f:
        lines = f.readlines()
    valid_lines = []
    for line in lines:
        if line.strip().startswith("//"):
            continue
        valid_lines.append(line)
    json_text = "".join(valid_lines).strip()
    if not json_text:
        return None, lines
    try:
        data = json.loads(json_text)
        return data, lines
    except Exception as e:
        logger.error(f"解析 links.json 出错: {e}")
        return None, lines

def comment_out_link_in_file(lines: list[str], link_str: str):
    target_quote = f"\"{link_str}\""
    for i, line in enumerate(lines):
        sline = line.strip()
        if sline.startswith("//"):
            continue
        if target_quote in sline:
            lines[i] = "// " + line
            break

def rewrite_links_file(lines: list[str]):
    file_path = "links.json"
    with open(file_path, "w", encoding="utf-8") as f:
        f.writelines(lines)

def is_valid_video(file_path: str) -> bool:
    if not os.path.exists(file_path):
        return False
    try:
        w, h, d, _ = get_video_info(file_path)
        return (w > 0 and h > 0 and d > 0)
    except:
        return False

def get_video_info(file_path: str) -> tuple[int, int, float, bool]:
    if not os.path.exists(file_path):
        return (0, 0, 0.0, False)
    try:
        cmd = [
            "ffprobe",
            "-v", "quiet",
            "-print_format", "json",
            "-show_streams",
            file_path
        ]
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode != 0:
            return (0, 0, 0.0, False)

        info = json.loads(result.stdout)
        streams = info.get("streams", [])
        w, h, duration = 0, 0, 0.0
        has_audio = False
        for s in streams:
            codec_type = s.get("codec_type", "")
            if codec_type == "video":
                w = int(float(s.get("width", 0)))
                h = int(float(s.get("height", 0)))
                try:
                    duration = float(s.get("duration", 0.0))
                except:
                    duration = 0.0
            elif codec_type == "audio":
                has_audio = True
        return (w, h, duration, has_audio)
    except:
        return (0, 0, 0.0, False)

def generate_video_thumb(file_path: str, start_sec=0) -> str:
    if not os.path.exists(file_path):
        return ""
    out_thumb = file_path + "_thumb.jpg"
    try:
        cmd = [
            "ffmpeg",
            "-y",
            "-ss", str(start_sec),
            "-i", file_path,
            "-vframes", "1",
            "-f", "image2",
            out_thumb
        ]
        subprocess.run(cmd, capture_output=True)
        if os.path.exists(out_thumb):
            return out_thumb
        return ""
    except:
        return ""

def make_zip_of_media(file_paths: list[str], work_id: str) -> str:
    if not file_paths:
        return ""
    zip_name = f"xhs_{work_id}_{int(time.time())}.zip"
    zip_path = os.path.join(BASE_DOWNLOAD_FOLDER, zip_name)
    try:
        with zipfile.ZipFile(zip_path, "w", compression=zipfile.ZIP_DEFLATED) as zf:
            for fp in file_paths:
                if os.path.isfile(fp):
                    arcname = os.path.basename(fp)
                    zf.write(fp, arcname)
        return zip_path
    except Exception as e:
        logger.error(f"打包 ZIP 失败: {e}")
        return ""

def extract_xhs_links(text: str) -> list[str]:
    pattern = r"(https?://[^\s]+)"
    raw_links = re.findall(pattern, text)
    all_links = []
    for link in raw_links:
        link = link.strip().rstrip('.,?!，')
        lower_link = link.lower()
        if "xhslink.com" in lower_link:
            all_links.append(link)
        elif "www.xiaohongshu.com/discovery/item/" in lower_link:
            link = link.replace("/discovery/item/", "/explore/")
            all_links.append(link)
        elif "www.xiaohongshu.com/explore/" in lower_link:
            all_links.append(link)
        else:
            pass
    return list(dict.fromkeys(all_links))

# # ------------------- TikHub API 获取小红书媒体链接（已弃用，本地API已支持） -------------------
# # 注意：保留此函数代码仅供参考，实际已改用本地API的动图地址
# """
# async def fetch_tikhub_media_urls(note_id: str) -> tuple[list[str], list[str]]:
#     从TikHub API获取小红书帖子的媒体URL（图片和视频）
#     
#     Args:
#         note_id: 小红书作品ID
#     
#     Returns:
#         tuple[list[str], list[str]]: (图片URL列表, 视频URL列表)
#     """
#     image_urls = []
#     video_urls = []
#     
#     # 设置最大重试次数（总共尝试3次：1次初始 + 2次重试）
#     max_retries = 2
#     retry_delay = 10  # 重试间隔，单位：秒
#     
#     for attempt in range(max_retries + 1):
#         try:
#             if attempt > 0:
#                 logger.info(f"正在进行第{attempt}次重试获取TikHub媒体链接: note_id={note_id}，等待{retry_delay}秒...")
#                 await asyncio.sleep(retry_delay)  # 重试前等待
#             
#             api_url = f"{TIKHUB_API_URL}?note_id={note_id}"
#             headers = {
#                 'accept': 'application/json',
#                 'Authorization': f'Bearer {XHS_TIKHUB_API_TOKEN}'
#             }
#             
#             logger.info(f"从TikHub API获取媒体链接: note_id={note_id}" + (f" (尝试 {attempt+1}/{max_retries+1})" if attempt > 0 else ""))
#             response = await asyncio.to_thread(requests.get, api_url, headers=headers, timeout=30)
#             response.raise_for_status()
#             data = response.json()
#             
#             # 检查响应数据
#             if not data or not isinstance(data, dict):
#                 logger.error(f"TikHub API返回数据格式异常: {data}" + (f" (尝试 {attempt+1}/{max_retries+1})" if attempt > 0 else ""))
#                 if attempt < max_retries:
#                     continue  # 重试
#                 return [], []
#             
#             # 提取媒体URL
#             # 路径: data → data → [0] → note_list → [0]
#             try:
#                 # Attempt to navigate to the structure containing 'note_list'
#                 # Common successful path: response_json -> "data" (dict) -> "data" (list) -> [0] (dict, this is actual_note_container)
#                 # Observed error path: response_json -> "data" (dict, this is actual_note_container, as it directly contains "note_list")
#                 
#                 actual_note_container = None
#                 possible_outer_data_dict = data.get("data") # data is the full json response
# 
#                 if isinstance(possible_outer_data_dict, dict):
#                     # Try common path: response.data.data[0]
#                     possible_inner_data_list = possible_outer_data_dict.get("data")
#                     if isinstance(possible_inner_data_list, list) and len(possible_inner_data_list) > 0 and isinstance(possible_inner_data_list[0], dict):
#                         actual_note_container = possible_inner_data_list[0]
#                         logger.debug("TikHub parser: Using path response.data.data[0]")
#                     elif "note_list" in possible_outer_data_dict:
#                         # Fallback: response.data is the container (observed error case described by user)
#                         actual_note_container = possible_outer_data_dict
#                         logger.debug("TikHub parser: Using path response.data as container")
#                 
#                 # Fallback: if top-level 'data' key was missing or didn't lead to a container,
#                 # check if the full response itself is the container.
#                 if actual_note_container is None and isinstance(data, dict) and "note_list" in data:
#                     actual_note_container = data
#                     logger.debug("TikHub parser: Using full response as container")
# 
#                 if actual_note_container is None:
#                     logger.error(f"TikHub API返回数据中未能定位到有效的note容器。原始数据: {data}" + (f" (尝试 {attempt+1}/{max_retries+1})" if attempt > 0 else ""))
#                     if attempt < max_retries:
#                         continue  # 重试
#                     return [], []
# 
#                 note_list = actual_note_container.get("note_list", [])
#                 if not note_list or len(note_list) == 0 or not isinstance(note_list[0], dict): # Ensure note_list[0] is a dict
#                     logger.error(f"TikHub API返回数据中未找到note_list或其内容格式不正确于: {actual_note_container}" + (f" (尝试 {attempt+1}/{max_retries+1})" if attempt > 0 else ""))
#                     if attempt < max_retries:
#                         continue  # 重试
#                     return [], []
#                     
#                 first_note = note_list[0]
#                 
#                 # 打印TikHub API原始返回的媒体链接
#                 logger.info("=" * 80)
#                 logger.info(f"🔗 【TikHub API原始返回的媒体链接】 作品ID: {note_id}")
#                 logger.info("=" * 80)
#                 
#                 # 提取图片列表
#                 images_list = first_note.get("images_list", [])
#                 for idx, img in enumerate(images_list, 1):
#                     # 高清原图链接: images_list → [n] → original
#                     original_url = img.get("original", "")
#                     if original_url:
#                         image_urls.append(original_url)
#                         logger.info(f"📷 TikHub图片 [{idx}]: {original_url}")
#                     
#                     # 提取视频链接: images_list → [n] → live_photo → media → stream → h265 → [0] → master_url
#                     live_photo = img.get("live_photo", {})
#                     if live_photo:
#                         media = live_photo.get("media", {})
#                         stream = media.get("stream", {})
#                         
#                         # 先尝试获取h265编码的视频
#                         h265_list = stream.get("h265", [])
#                         if h265_list and len(h265_list) > 0:
#                             master_url = h265_list[0].get("master_url", "")
#                             if master_url:
#                                 video_urls.append(master_url)
#                                 logger.info(f"🎬 TikHub视频 [{idx}] (h265编码): {master_url}")
#                         
#                         # 如果没有h265，尝试获取h264编码的视频
#                         else:
#                             h264_list = stream.get("h264", [])
#                             if h264_list and len(h264_list) > 0:
#                                 master_url = h264_list[0].get("master_url", "")
#                                 if master_url:
#                                     video_urls.append(master_url)
#                                     logger.info(f"🎬 TikHub视频 [{idx}] (h264编码): {master_url}")
#                         
#                         # 如果还有其他编码如av1或h266，可以在这里添加类似的处理
#                 
#                 logger.info("=" * 80)
#                 logger.info(f"✅ TikHub API获取完成: 图片={len(image_urls)}个, 视频={len(video_urls)}个")
#                 logger.info("=" * 80)
#                 return image_urls, video_urls
#             except Exception as e:
#                 logger.error(f"解析TikHub API返回数据时出现异常: {e}" + (f" (尝试 {attempt+1}/{max_retries+1})" if attempt > 0 else ""), exc_info=True)
#                 if attempt < max_retries:
#                     continue  # 重试
#                 return [], []
#                 
#         except Exception as e:
#             logger.error(f"从TikHub API获取媒体链接失败: {e}" + (f" (尝试 {attempt+1}/{max_retries+1})" if attempt > 0 else ""), exc_info=True)
#             if attempt < max_retries:
#                 continue  # 重试
#     
#     # 所有重试都失败
#     return [], []
# """
# 
# ------------------- Bot 命令处理 -------------------
@bot.on_message(filters.command("start") & filters.private)
async def start_handler(client: Client, message: Message):
    logger.info(f"收到 /start 命令 - 来自: {message.chat.id}, 用户: {message.from_user.first_name if message.from_user else 'Unknown'}")
    if message.chat.id != TARGET_CHAT_ID:
        logger.warning(f"未授权用户 {message.chat.id} 尝试使用 /start 命令")
        return
    await ephemeral_send_text("欢迎使用小红书下载 Bot，请直接发送小红书链接给我。", delay=30)

    links_data, file_lines = read_links_json()
    if not links_data:
        await ephemeral_send_text("未能解析 links.json，或文件不存在。", delay=30)
        return

    links_list = links_data.get("links", [])
    if not links_list:
        await ephemeral_send_text("links.json 中没有可处理的链接。", delay=30)
        return

    links_list = [str(x).strip() for x in links_list if str(x).strip()]
    results = await process_links_batch(links_list, file_lines, from_json=True)

    # 收集失败的链接
    failed_links = [link_str for link_str, success in results if not success and success != "deleted"]
    
    # 如果有失败的链接，且失败原因可能是cookie问题，尝试更新cookie后重试
    if failed_links and ERROR_TRACKING.get("consecutive_empty_data_errors", 0) >= ERROR_THRESHOLD:
        logger.info(f"检测到 {len(failed_links)} 个链接处理失败，尝试更新cookie后重试...")
        await ephemeral_send_text(f"检测到 {len(failed_links)} 个链接失败，正在更新cookie并重试...", delay=10)
        
        # 更新cookie并重启API
        cookie_success, cookie_msg = update_xhs_cookie()
        if cookie_success:
            logger.info(f"Cookie更新成功，准备重启API服务: {cookie_msg}")
            service_ok = restart_xhs_api_service()
            if service_ok:
                logger.info("API服务重启成功，开始重试失败的链接")
                await ephemeral_send_text(f"Cookie已更新，正在重试 {len(failed_links)} 个失败的链接...", delay=10)
                
                # 重置错误计数
                ERROR_TRACKING["consecutive_errors"] = 0
                ERROR_TRACKING["consecutive_empty_data_errors"] = 0
                
                # 重试失败的链接
                retry_results = await process_links_batch(failed_links, file_lines, from_json=True)
                
                # 更新结果
                retry_dict = dict(retry_results)
                for i, (link_str, original_success) in enumerate(results):
                    if link_str in retry_dict:
                        results[i] = (link_str, retry_dict[link_str])
            else:
                logger.error("API服务重启失败，无法重试")
        else:
            logger.warning(f"Cookie更新失败: {cookie_msg}")
    
    # 收集重试后仍然失败的链接，使用TikHub API检查
    final_failed_links = [link_str for link_str, success in results if not success and success != "deleted"]
    tikhub_deleted_links = []
    
    if final_failed_links and XHS_TIKHUB_API_TOKEN:
        logger.info(f"有 {len(final_failed_links)} 个链接重试后仍失败，使用TikHub API进行最终检查...")
        await ephemeral_send_text(f"正在使用TikHub API检查 {len(final_failed_links)} 个失败链接的状态...", delay=10)
        
        for link_str in final_failed_links:
            # 从链接中提取作品ID
            note_id = None
            import re
            patterns = [
                r'/explore/([a-f0-9]{24})',
                r'/discovery/item/([a-f0-9]{24})',
                r'note_id=([a-f0-9]{24})',
                r'([a-f0-9]{24})',
            ]
            
            for pattern in patterns:
                match = re.search(pattern, link_str)
                if match:
                    note_id = match.group(1)
                    break
            
            if note_id:
                # 使用TikHub API检查
                tikhub_result = await check_note_deleted_via_tikhub(note_id)
                
                if tikhub_result['deleted']:
                    # 更新结果为已删除
                    for i, (l, s) in enumerate(results):
                        if l == link_str:
                            results[i] = (link_str, "deleted")
                            tikhub_deleted_links.append(link_str)
                            logger.info(f"TikHub API确认作品已删除: {link_str}")
                            break
                elif tikhub_result['exists'] and tikhub_result['media_urls']:
                    # 作品存在且有媒体资源，尝试使用TikHub的资源下载
                    logger.info(f"TikHub API确认作品存在，尝试使用TikHub资源下载: {link_str}")
                    
                    # 构造一个简化的xhs_data格式用于下载
                    tikhub_xhs_data = {
                        "code": 0,
                        "data": {
                            "作品ID": note_id,
                            "作品链接": link_str,
                            "媒体类型": tikhub_result['type'],
                            "媒体URLs": tikhub_result['media_urls'],
                            "来源": "TikHub API"
                        }
                    }
                    
                    # 尝试处理这个作品
                    try:
                        success = await process_tikhub_media(link_str, tikhub_xhs_data, tikhub_result)
                        if success:
                            # 更新结果为成功
                            for i, (l, s) in enumerate(results):
                                if l == link_str:
                                    results[i] = (link_str, True)
                                    logger.info(f"TikHub API资源下载成功: {link_str}")
                                    break
                        else:
                            logger.warning(f"TikHub API资源下载失败: {link_str}")
                    except Exception as e:
                        logger.error(f"处理TikHub资源时出错: {e}")
                        
                elif tikhub_result['exists']:
                    logger.info(f"TikHub API确认作品存在但无媒体资源: {link_str}")
                else:
                    logger.warning(f"TikHub API无法确认作品状态: {link_str}")

    # 重新统计结果
    if len(results) > 5:
        total_count = len(results)
        # 统计不同状态的数量
        success_count = sum(1 for r in results if r[1] == True)
        deleted_count = sum(1 for r in results if r[1] == "deleted")
        fail_count = total_count - success_count - deleted_count
        
        summary_text = (
            f"已完成全部链接处理。\n"
            f"共处理 {total_count} 个链接：\n"
            f"✅ 成功 {success_count} 个\n"
        )
        
        if deleted_count > 0:
            summary_text += f"🗑️ 已删除 {deleted_count} 个\n"
        
        if fail_count > 0:
            summary_text += f"❌ 失败 {fail_count} 个\n"
        
        # 如果有重试，添加说明
        if failed_links:
            retry_success_count = sum(1 for link_str, success in results if link_str in failed_links and success == True)
            if retry_success_count > 0:
                summary_text += f"\n📊 其中 {retry_success_count} 个链接在更新cookie后重试成功"
        
        # 如果有通过TikHub确认删除的，添加说明
        if tikhub_deleted_links:
            summary_text += f"\n🔍 TikHub API确认 {len(tikhub_deleted_links)} 个作品已删除"
        
        # 最终还是失败的链接（不包括已删除的）
        final_failed = [link_str for link_str, success in results if not success and success != "deleted"]
        if final_failed:
            summary_text += "\n\n❌ 以下链接处理失败：\n"
            for link_str in final_failed:
                summary_text += f"{link_str}\n"
        
        # 已删除的链接列表
        deleted_links = [link_str for link_str, success in results if success == "deleted"]
        if deleted_links:
            summary_text += "\n\n🗑️ 以下作品已被删除：\n"
            for link_str in deleted_links:
                summary_text += f"{link_str}\n"
        
        await ephemeral_send_text(summary_text, delay=30)
    else:
        await ephemeral_send_text("links.json 中的链接处理完毕。", delay=30)

@bot.on_message(filters.text & filters.private)
async def text_handler(client: Client, message: Message):
    logger.info(f"收到文本消息 - 来自: {message.chat.id}, 用户: {message.from_user.first_name if message.from_user else 'Unknown'}")
    logger.info(f"消息内容: {message.text[:100]}...") if len(message.text) > 100 else logger.info(f"消息内容: {message.text}")
    if message.chat.id != TARGET_CHAT_ID:
        logger.warning(f"未授权用户 {message.chat.id} 发送消息: {message.text[:50]}...")
        return
    chat_id = message.chat.id
    if chat_id not in user_incoming_messages:
        user_incoming_messages[chat_id] = {"messages": [], "timer_task": None}
    user_incoming_messages[chat_id]["messages"].append(message)
    schedule_handle_user_texts(chat_id)

@bot.on_message(filters.command("restart_api") & filters.private)
async def restart_api_handler(client: Client, message: Message):
    logger.info(f"收到 /restart_api 命令 - 来自: {message.chat.id}")
    if message.chat.id != TARGET_CHAT_ID:
        logger.warning(f"未授权用户 {message.chat.id} 尝试使用 /restart_api 命令")
        return
    await ephemeral_send_text("正在尝试重启XHS API服务...", delay=60)
    success = restart_xhs_api_service()
    if success:
        await ephemeral_send_text("XHS API服务已成功重启", delay=10)
    else:
        await safe_send_long_text("XHS API服务重启失败，请检查服务状态")

@bot.on_message(filters.command("update_cookie") & filters.private)
async def update_cookie_handler(client: Client, message: Message):
    logger.info(f"收到 /update_cookie 命令 - 来自: {message.chat.id}")
    if message.chat.id != TARGET_CHAT_ID:
        logger.warning(f"未授权用户 {message.chat.id} 尝试使用 /update_cookie 命令")
        return
    await ephemeral_send_text("正在更新小红书cookie...", delay=60)
    success, msg = update_xhs_cookie()
    if not success:
        await safe_send_long_text(f"更新cookie失败：{msg}")
        return
    await safe_send_long_text(f"Cookie更新结果：{msg}")
    # 重启服务
    await ephemeral_send_text("正在重启XHS API服务...", delay=60)
    service_ok = restart_xhs_api_service()
    if service_ok:
        await ephemeral_send_text("XHS API服务已成功重启，新cookie已生效", delay=10)
    else:
        await safe_send_long_text("XHS API服务重启失败，请手动检查。")

def unzip_all_files(directory: str) -> tuple[int, int]:
    success_count = 0
    fail_count = 0
    try:
        zip_files = [f for f in os.listdir(directory) if f.endswith('.zip')]
        for zip_file in zip_files:
            zip_path = os.path.join(directory, zip_file)
            try:
                extract_dir = os.path.join(directory, zip_file[:-4])
                os.makedirs(extract_dir, exist_ok=True)
                with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                    zip_ref.extractall(extract_dir)
                os.remove(zip_path)
                success_count += 1
                logger.info(f"成功解压并删除: {zip_file}")
            except Exception as e:
                logger.error(f"解压 {zip_file} 失败: {e}")
                fail_count += 1
        return success_count, fail_count
    except Exception as e:
        logger.error(f"处理ZIP文件时出错: {e}")
        return success_count, fail_count

@bot.on_message(filters.command("unzip_all") & filters.private)
async def unzip_all_handler(client: Client, message: Message):
    logger.info(f"收到 /unzip_all 命令 - 来自: {message.chat.id}")
    if message.chat.id != TARGET_CHAT_ID:
        logger.warning(f"未授权用户 {message.chat.id} 尝试使用 /unzip_all 命令")
        return
    await ephemeral_send_text("正在解压所有ZIP文件，请稍候...", delay=60)
    if not os.path.exists(BASE_DOWNLOAD_FOLDER):
        await safe_send_long_text("下载目录不存在！")
        return
    success_count, fail_count = unzip_all_files(BASE_DOWNLOAD_FOLDER)
    total = success_count + fail_count
    if total == 0:
        await safe_send_long_text("没有找到需要解压的ZIP文件。")
    else:
        result_text = f"解压完成！\n共处理 {total} 个ZIP文件\n成功: {success_count} 个\n失败: {fail_count} 个"
        if fail_count > 0:
            result_text += "\n\n请查看日志了解失败详情。"
        await safe_send_long_text(result_text)

# 处理回调查询（按钮点击）
@bot.on_callback_query()
async def callback_query_handler(client: Client, callback_query):
    """处理inline键盘按钮的回调"""
    try:
        data = callback_query.data
        logger.info(f"收到回调查询: {data} - 来自用户: {callback_query.from_user.id}")
        
        # 处理重新处理按钮
        if data.startswith("reprocess_"):
            # 格式: reprocess_{note_id}
            parts = data.split("_", 1)
            if len(parts) >= 2:
                note_id = parts[1]
                
                # 回复用户正在处理
                await callback_query.answer("正在重新处理，请稍候...", show_alert=False)
                
                # 先从数据库获取链接和短链接信息
                work_link = None
                saved_short_link = None  # 保存原始短链接
                if supabase:
                    try:
                        # 获取短链接和普通链接
                        result = supabase.table("xhs_notes").select("short_link, note_link").eq("id", note_id).execute()
                        if result.data and len(result.data) > 0:
                            # 保存短链接（如果存在）
                            saved_short_link = result.data[0].get("short_link")
                            
                            # 优先使用短链接进行处理
                            if saved_short_link:
                                work_link = saved_short_link
                                logger.info(f"从数据库获取到短链接: {work_link}")
                            else:
                                work_link = result.data[0].get("note_link")
                                logger.info(f"从数据库获取到笔记链接: {work_link}")
                    except Exception as e:
                        logger.error(f"获取笔记链接失败: {e}")
                
                if not work_link:
                    # 如果没有获取到链接，尝试从作品ID构造
                    work_link = f"https://www.xiaohongshu.com/explore/{note_id}"
                    logger.info(f"使用作品ID构造链接: {work_link}")
                
                # 删除原消息
                try:
                    await callback_query.message.delete()
                except Exception as e:
                    logger.warning(f"删除原消息失败: {e}")
                
                # 删除数据库记录
                if supabase:
                    try:
                        # 删除notes表记录
                        result = supabase.table("xhs_notes").delete().eq("id", note_id).execute()
                        logger.info(f"已删除数据库中的笔记记录: {note_id}")
                    except Exception as e:
                        logger.error(f"删除数据库记录失败: {e}")
                        await safe_send_long_text(f"删除数据库记录失败: {e}")
                        return
                
                # 重新处理链接
                logger.info(f"开始重新处理笔记 {note_id}，链接: {work_link}，保留短链接: {saved_short_link}")
                await ephemeral_send_text(f"已删除缓存，正在重新处理链接:\n{work_link}", delay=10)
                
                # 直接调用处理函数处理链接，传递保存的短链接信息
                links = extract_xhs_links(work_link)
                if links:
                    logger.info(f"重新处理提取到的链接: {links}")
                    # 传递原始短链接信息
                    result = await process_links_batch(links, preserved_short_link=saved_short_link)
                    if result:
                        await ephemeral_send_text(f"✅ 重新处理完成", delay=5)
                    else:
                        await ephemeral_send_text(f"❌ 重新处理失败，请查看日志", delay=30)
                else:
                    await safe_send_long_text(f"未能从文本中提取到有效的小红书链接")
            else:
                await callback_query.answer("重新处理失败：数据格式错误", show_alert=True)
        
        # 处理保存JSON按钮
        elif data.startswith("savejson_"):
            # 格式: savejson_{note_id}
            parts = data.split("_", 1)
            if len(parts) >= 2:
                note_id = parts[1]
                
                # 回复用户正在处理
                await callback_query.answer("正在获取数据并保存JSON...", show_alert=False)
                
                # 从数据库获取链接信息
                work_link = None
                if supabase:
                    try:
                        # 获取短链接和普通链接
                        result = supabase.table("xhs_notes").select("short_link, note_link").eq("id", note_id).execute()
                        if result.data and len(result.data) > 0:
                            # 优先使用短链接
                            short_link = result.data[0].get("short_link")
                            if short_link:
                                work_link = short_link
                                logger.info(f"使用短链接获取JSON: {work_link}")
                            else:
                                work_link = result.data[0].get("note_link")
                                logger.info(f"使用笔记链接获取JSON: {work_link}")
                    except Exception as e:
                        logger.error(f"获取笔记链接失败: {e}")
                
                if not work_link:
                    # 如果没有获取到链接，尝试从作品ID构造
                    work_link = f"https://www.xiaohongshu.com/explore/{note_id}"
                    logger.info(f"使用作品ID构造链接获取JSON: {work_link}")
                
                # 调用API获取数据（使用较短的超时）
                try:
                    logger.info(f"正在请求API获取JSON数据: {work_link}")
                    start_time = time.time()
                    xhs_data = await fetch_xhs_data(work_link, timeout=10)  # 保存JSON使用10秒超时
                    elapsed_time = time.time() - start_time
                    logger.info(f"API响应耗时: {elapsed_time:.2f}秒")
                    
                    # 保存JSON功能：无论API返回什么都要保存
                    if xhs_data:
                        # 保存JSON到文件
                        import json
                        from datetime import datetime
                        
                        # 创建保存目录
                        json_dir = os.path.join(BASE_DOWNLOAD_FOLDER, "json_saves")
                        os.makedirs(json_dir, exist_ok=True)
                        
                        # 生成文件名
                        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                        json_filename = f"xhs_{note_id}_{timestamp}.json"
                        json_filepath = os.path.join(json_dir, json_filename)
                        
                        # 保存JSON文件
                        with open(json_filepath, 'w', encoding='utf-8') as f:
                            json.dump(xhs_data, f, ensure_ascii=False, indent=2)
                        
                        logger.info(f"JSON数据已保存到: {json_filepath}")
                        
                        # 发送JSON文件给用户
                        try:
                            async with TELEGRAM_SEND_LOCK:
                                await bot.send_document(
                                    chat_id=callback_query.from_user.id,
                                document=json_filepath,
                                caption=f"📋 笔记 {note_id} 的完整JSON数据\n📁 文件名: {json_filename}",
                                file_name=json_filename
                            )
                            # JSON文件已通过send_document发送，不需要额外的文字说明
                            logger.info(f"JSON数据已保存并发送: {json_filepath}")
                        except Exception as e:
                            logger.error(f"发送JSON文件失败: {e}")
                            await safe_send_long_text(f"✅ JSON已保存到本地\n路径: {json_filepath}\n\n❌ 但发送文件失败: {e}")
                    else:
                        # API没有返回数据
                        await safe_send_long_text(f"❌ API未返回数据，请检查链接或稍后重试")
                        
                except Exception as e:
                    logger.error(f"保存JSON时出错: {e}", exc_info=True)
                    await safe_send_long_text(f"❌ 保存JSON失败: {e}")
            else:
                await callback_query.answer("保存JSON失败：数据格式错误", show_alert=True)
        
        # 处理订阅作者按钮
        elif data.startswith("sub_"):
            # 格式: sub_{author_id}
            parts = data.split("_", 1)
            if len(parts) >= 2:
                author_id = parts[1]
                
                if not author_id:
                    await callback_query.answer("作者ID无效", show_alert=True)
                    return
                
                if supabase:
                    try:
                        # 获取作者当前信息（作者记录必定存在，因为处理作品时已创建）
                        check_result = await asyncio.to_thread(
                            supabase.table("xhs_user").select("userid, nickname, sub").eq("userid", author_id).execute
                        )
                        
                        if check_result.data and len(check_result.data) > 0:
                            current_sub = check_result.data[0].get("sub", False)
                            author_nickname = check_result.data[0].get("nickname", "未知作者")
                            
                            if current_sub:
                                # 已经订阅，切换为取消订阅
                                update_result = await asyncio.to_thread(
                                    supabase.table("xhs_user").update({"sub": False}).eq("userid", author_id).execute
                                )
                                logger.info(f"已取消订阅作者 {author_id} ({author_nickname})")
                                await callback_query.answer(f"❌ 已取消订阅作者 {author_nickname}", show_alert=True)
                            else:
                                # 未订阅，设置为订阅
                                update_result = await asyncio.to_thread(
                                    supabase.table("xhs_user").update({"sub": True}).eq("userid", author_id).execute
                                )
                                logger.info(f"已订阅作者 {author_id} ({author_nickname})")
                                await callback_query.answer(f"✅ 成功订阅作者 {author_nickname}", show_alert=True)
                        else:
                            # 理论上不应该发生，因为处理作品时已创建作者记录
                            logger.warning(f"未找到作者 {author_id} 的记录（这不应该发生）")
                            await callback_query.answer("❌ 作者信息未找到，请重新处理该作品", show_alert=True)
                                
                    except Exception as e:
                        logger.error(f"订阅/取消订阅作者时出错: {e}", exc_info=True)
                        await callback_query.answer(f"❌ 操作失败: {str(e)}", show_alert=True)
                else:
                    await callback_query.answer("数据库未连接", show_alert=True)
            else:
                await callback_query.answer("订阅失败：数据格式错误", show_alert=True)
        
        else:
            # 其他未知的callback
            await callback_query.answer()
            
    except Exception as e:
        logger.error(f"处理回调查询时出错: {e}", exc_info=True)
        await callback_query.answer("处理失败，请重试", show_alert=True)

# 调试: 捕获所有未处理的消息 (使用 group=-1 确保最后执行)
@bot.on_message(group=-1)
async def debug_unhandled_messages(client: Client, message: Message):
    logger.info(f"[DEBUG-UNHANDLED] 收到消息 - Chat ID: {message.chat.id}, Type: {message.chat.type}, Text: {message.text if message.text else 'Non-text'}")
    if message.from_user:
        logger.info(f"[DEBUG-UNHANDLED] 发送者: {message.from_user.first_name} (ID: {message.from_user.id})")

# ------------------- 主入口 -------------------
if __name__ == "__main__":
    # 设置 asyncio 全局异常处理
    loop = asyncio.get_event_loop()
    loop.set_exception_handler(handle_unhandled_exception)

    logger.info("Bot 即将启动，先更新 cookie 并确保 XHS API 服务正常运行...")
    
    # 每次启动都更新 cookie 并重启 API 服务，确保使用最新的 cookie
    try:
        # 步骤1: 先更新 cookie
        logger.info("正在更新小红书 cookie...")
        cookie_success, cookie_msg = update_xhs_cookie()
        if cookie_success:
            logger.info(f"Cookie 更新成功: {cookie_msg}")
        else:
            logger.warning(f"Cookie 更新失败: {cookie_msg}")
            # 即使 cookie 更新失败也继续，可能是网络问题，使用现有的 cookie
        
        # 步骤2: 检查并停止现有服务
        session_name = "server"
        tmux_check_cmd = f"tmux has-session -t {session_name} 2>/dev/null"
        tmux_result = subprocess.run(tmux_check_cmd, shell=True)
        
        process_check_cmd = f"ps aux | grep 'python3 main.py api' | grep -v grep"
        process_result = subprocess.run(process_check_cmd, shell=True, capture_output=True, text=True)
        
        tmux_exists = tmux_result.returncode == 0
        process_running = len(process_result.stdout.strip()) > 0
        
        if tmux_exists or process_running:
            logger.info("检测到 XHS API 服务正在运行，准备重启...")
            # 停止现有服务
            if process_running:
                subprocess.run("pkill -f 'python3.*main.py api'", shell=True)
                logger.info("已终止现有的 API 进程")
            if tmux_exists:
                subprocess.run(f"tmux kill-session -t {session_name}", shell=True)
                logger.info(f"已关闭 tmux 会话: {session_name}")
            time.sleep(2)  # 等待进程完全停止
        
        # 步骤3: 启动新的 API 服务
        logger.info("正在启动 XHS API 服务（使用更新后的 cookie）...")
        restart_xhs_api_service()
        logger.info("XHS API 服务已成功启动并准备就绪")
        
    except Exception as e:
        logger.error(f"更新 cookie 或重启服务时出错: {e}")
        logger.info("尝试直接重启 XHS API 服务...")
        restart_xhs_api_service()

    logger.info("启动 Telegram Bot...")
    logger.info(f"Bot token 长度: {len(BOT_TOKEN) if BOT_TOKEN else 0}")
    logger.info(f"API_ID: {API_ID}")
    logger.info(f"API_HASH 长度: {len(API_HASH) if API_HASH else 0}")
    
    
    try:
        logger.info("正在初始化并连接 Telegram Bot...")
        
        # 发送启动通知的函数
        async def process_empty_file_ids_once(skip_tikhub=True):
            """处理数据库中file_id为空的记录（单次）"""
            if not supabase:
                logger.info("Supabase未初始化，跳过空file_id处理")
                return 0
            
            try:
                logger.info("开始查询file_id为空的记录...")
                
                # 查询file_id为空或null且未标记为失败的记录
                response = await asyncio.to_thread(
                    supabase.table("xhs_notes")
                    .select("id, note_link, short_link")
                    .or_("file_id.is.null,file_id.eq.")
                    .eq("process_failed", False)  # 跳过已标记为失败的记录
                    .limit(100)  # 每批处理100条，避免一次处理过多
                    .execute
                )
                
                if not response.data:
                    logger.info("没有找到file_id为空的记录")
                    return 0
                
                empty_records = response.data
                logger.info(f"找到 {len(empty_records)} 条file_id为空的记录，开始处理...")
                
                processed_count = 0
                # 逐个处理
                for idx, record in enumerate(empty_records, 1):
                    note_id = record.get("id")
                    note_link = record.get("note_link")
                    short_link = record.get("short_link")
                    
                    # 优先使用short_link，如果没有则使用note_link
                    link_to_process = short_link if short_link else note_link
                    
                    if not link_to_process:
                        logger.warning(f"记录 {note_id} 没有有效的链接，跳过")
                        continue
                    
                    logger.info(f"处理进度 [{idx}/{len(empty_records)}] - 正在处理笔记 {note_id}: {link_to_process}")
                    
                    try:
                        # 直接调用fetch和处理，跳过重试机制
                        xhs_data = await fetch_xhs_data(link_to_process)
                        
                        if not xhs_data or not xhs_data.get("data"):
                            logger.warning(f"笔记 {note_id} 获取数据失败")
                            
                            # 累积错误计数
                            ERROR_TRACKING["consecutive_empty_data_errors"] += 1
                            ERROR_TRACKING["consecutive_errors"] += 1
                            
                            # 检查是否需要重启API服务
                            if ERROR_TRACKING["consecutive_empty_data_errors"] >= ERROR_THRESHOLD:
                                logger.warning(f"连续 {ERROR_TRACKING['consecutive_empty_data_errors']} 次API返回空数据，触发自动处理")
                                handled, is_fatal = check_and_handle_api_errors("API返回数据为空或无 'data' 字段")
                                
                                if handled:
                                    if is_fatal:
                                        logger.error("API服务无法恢复，停止自动处理")
                                        return processed_count
                                    else:
                                        # 服务已重启，重置错误计数在check_and_handle_api_errors中已完成
                                        # 可以继续处理或等待下一批
                                        logger.info("API服务已重启，继续处理")
                                        # 重新尝试当前记录
                                        xhs_data = await fetch_xhs_data(link_to_process)
                                        if not xhs_data or not xhs_data.get("data"):
                                            # 重启后仍然失败，标记为失败
                                            logger.warning(f"重启后笔记 {note_id} 仍获取失败，标记为失败")
                                            try:
                                                await asyncio.to_thread(
                                                    supabase.table("xhs_notes")
                                                    .update({"process_failed": True})
                                                    .eq("id", note_id)
                                                    .execute
                                                )
                                                logger.info(f"已将笔记 {note_id} 标记为处理失败")
                                            except Exception as update_err:
                                                logger.error(f"更新笔记 {note_id} 失败标记时出错: {update_err}")
                                            continue
                                        # 如果重启后成功获取数据，继续处理
                            else:
                                # 未达到重启阈值，标记为失败并继续
                                try:
                                    await asyncio.to_thread(
                                        supabase.table("xhs_notes")
                                        .update({"process_failed": True})
                                        .eq("id", note_id)
                                        .execute
                                    )
                                    logger.info(f"已将笔记 {note_id} 标记为处理失败")
                                except Exception as update_err:
                                    logger.error(f"更新笔记 {note_id} 失败标记时出错: {update_err}")
                                continue
                        
                        # 如果是已删除的作品，也标记为失败
                        if xhs_data.get("code") == 400 and xhs_data.get("msg") == "作品已删除":
                            logger.info(f"笔记 {note_id} 已删除，标记为失败")
                            try:
                                await asyncio.to_thread(
                                    supabase.table("xhs_notes")
                                    .update({"process_failed": True})
                                    .eq("id", note_id)
                                    .execute
                                )
                                logger.info(f"已将已删除笔记 {note_id} 标记为处理失败")
                            except Exception as update_err:
                                logger.error(f"更新笔记 {note_id} 失败标记时出错: {update_err}")
                            continue
                        
                        # 处理内容
                        success = await process_single_link_content(link_to_process, xhs_data, None, from_json=False)
                        
                        if success:
                            logger.info(f"成功处理笔记 {note_id}")
                            processed_count += 1
                            # 成功处理后重置错误计数
                            ERROR_TRACKING["consecutive_errors"] = 0
                            ERROR_TRACKING["consecutive_empty_data_errors"] = 0
                        else:
                            logger.warning(f"处理笔记 {note_id} 失败，标记为失败")
                            # 处理失败也标记
                            try:
                                await asyncio.to_thread(
                                    supabase.table("xhs_notes")
                                    .update({"process_failed": True})
                                    .eq("id", note_id)
                                    .execute
                                )
                                logger.info(f"已将处理失败的笔记 {note_id} 标记为失败")
                            except Exception as update_err:
                                logger.error(f"更新笔记 {note_id} 失败标记时出错: {update_err}")
                        
                    except Exception as e:
                        logger.error(f"处理笔记 {note_id} 时出错: {e}，标记为失败")
                        # 异常也标记为失败
                        try:
                            await asyncio.to_thread(
                                supabase.table("xhs_notes")
                                .update({"process_failed": True})
                                .eq("id", note_id)
                                .execute
                            )
                            logger.info(f"已将出错的笔记 {note_id} 标记为处理失败")
                        except Exception as update_err:
                            logger.error(f"更新笔记 {note_id} 失败标记时出错: {update_err}")
                        continue
                
                logger.info(f"本批次处理完成，成功处理 {processed_count}/{len(empty_records)} 条记录")
                return len(empty_records)
                
            except Exception as e:
                logger.error(f"处理空file_id记录时出错: {e}")
                return 0
        
        async def process_empty_file_ids_loop():
            """无限循环处理数据库中file_id为空的记录"""
            logger.info("启动空file_id记录自动处理循环...")
            
            while True:
                try:
                    # 处理一批记录
                    processed = await process_empty_file_ids_once(skip_tikhub=True)
                    
                    if processed == 0:
                        # 没有记录需要处理，等待较长时间
                        logger.info("当前没有空file_id记录，等待5分钟后再次检查...")
                        await asyncio.sleep(300)  # 5分钟
                    else:
                        # 处理了一些记录，短暂等待后继续
                        logger.info("等待10秒后继续处理下一批...")
                        await asyncio.sleep(10)  # 10秒
                        
                except Exception as e:
                    logger.error(f"处理循环出错: {e}，等待30秒后重试...")
                    await asyncio.sleep(30)
        
        async def send_startup_notification():
            try:
                if TARGET_CHAT_ID:
                    await ephemeral_send_text(
                        "🤖 小红书下载 Bot 已启动\n"
                        "✅ 现在可以开始处理消息了\n"
                        "📝 发送小红书链接即可下载内容",
                        delay=60
                    )
                    logger.info(f"已向用户 {TARGET_CHAT_ID} 发送启动通知")
                else:
                    logger.warning("TARGET_CHAT_ID 未设置，跳过启动通知")
            except Exception as notify_error:
                logger.error(f"发送启动通知失败: {notify_error}")
                logger.error(f"错误类型: {type(notify_error).__name__}")
                import traceback
                logger.error(f"详细错误堆栈:\n{traceback.format_exc()}")
        
        # 使用 Pyrogram 的标准运行方式
        with bot:
            logger.info("Telegram Bot 已成功连接!")
            
            # 创建启动任务的异步函数
            async def startup_tasks():
                """执行所有启动任务"""
                # 发送启动通知
                await send_startup_notification()
                
                # 在后台启动空file_id记录处理循环
                logger.info("启动后台任务：自动处理空file_id记录...")
                asyncio.create_task(process_empty_file_ids_loop())
                
                logger.info("所有启动任务已完成")
            
            # 同步运行启动任务
            import asyncio
            loop = asyncio.get_event_loop()
            loop.run_until_complete(startup_tasks())
            
            logger.info("Bot 正在运行，等待消息...")
            
            # 使用 Pyrogram 的 idle 保持运行
            from pyrogram import idle
            idle()
            
            logger.info("Bot 已停止运行")
        
    except Exception as e:
        logger.error(f"启动 Telegram Bot 失败: {e}")
        logger.error(f"错误类型: {type(e).__name__}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
